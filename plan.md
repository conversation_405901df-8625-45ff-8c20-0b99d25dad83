# Plan: Credit-Based Payment System

## 1. Introduction
This document outlines the architectural plan for implementing a credit-based payment system. This system will integrate with existing microservices (`user-service`, `organisation-service`, `agent-service`, `api-gateway`) and introduce new functionalities primarily within the `payment-service`. The goal is to manage user/organisation credits, handle different subscription plans (Free and Pro), and deduct credits based on service usage, particularly agent interactions.

## 2. Core Concepts
*   **Credits**: A virtual currency used by organisations to pay for service usage.
*   **Token-to-Credit Conversion**: Assumed rate: **1000 tokens = 1 credit**. This rate should be configurable.
*   **Plans**:
    *   **Free Plan**: Provides an initial allocation of **20 credits**. Automatically assigned to an organisation upon creation.
    *   **Pro Plan**: Provides **100 credits**. This is an upgradable, paid plan.
*   **Organisation Credits**: The central credit balance held by an organisation, managed in `user-service` and updated by `payment-service`.
*   **Credit Ledger**: A detailed transaction log maintained by `payment-service` for all credit movements (allocations, deductions, purchases).
*   **Organisation Admin**: The user role responsible for managing the organisation's subscription and payments.

## 3. Actors & Responsibilities
*   **End User**: Interacts with services (e.g., agents), indirectly consuming credits from their organisation's balance.
*   **Organisation Admin**: Manages the organisation, invites users, and is responsible for upgrading to paid plans (e.g., Pro Plan) and handling payments.
*   **`user-service`**: Source of truth for user identity and organisation's current `credit_balance` and `current_plan_id`.
*   **`organisation-service`**: Manages organisation lifecycle; triggers default plan activation on creation.
*   **`agent-service` (or similar usage service)**: Calculates token usage per interaction, converts to credits, and requests deduction from `payment-service`. Logs detailed usage to `analytics-service`.
*   **`payment-service`**: Manages plan definitions, Stripe integration for payments/subscriptions, the `CreditLedger`, and orchestrates credit balance updates in `user-service`.
*   **`api-gateway`**: Routes external requests to appropriate services; handles authentication.
*   **`analytics-service`**: Stores detailed logs of service usage, including token counts and credits consumed, for analytical purposes.

## 4. High-Level Flows

### A. User Onboarding & Free Plan Activation
*New organisation creation triggers automatic activation of the Free Plan.*
```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Organisation Service
    participant Payment Service
    participant User Service

    Client->>API Gateway: Create Organisation Request (user_id, org_name)
    API Gateway->>Organisation Service: CreateOrganisation(user_id, org_name)
    Organisation Service->>Organisation Service: Store Organisation in DB
    Note over Organisation Service: User becomes admin of new org.
    Organisation Service-->>API Gateway: Organisation Created (org_id)
    API Gateway-->>Client: Organisation Created (org_id)

    %% Plan Activation (triggered by Organisation Service)
    Organisation Service->>Payment Service: ActivateDefaultPlan(org_id, default_plan_id="free")
    Payment Service->>Payment Service: Retrieve "Free Plan" details (20 credits)
    Payment Service->>User Service: AssignPlanToOrganisation(org_id, plan_id="free")
    User Service->>User Service: Update org.current_plan_id
    User Service-->>Payment Service: Plan Assigned
    Payment Service->>User Service: SetInitialOrganisationCredits(org_id, amount=20)
    User Service->>User Service: Set org.credit_balance = 20
    User Service-->>Payment Service: Credits Set
    Payment Service->>Payment Service: Log to CreditLedger (type: INITIAL_ALLOCATION, amount: +20)
    Payment Service-->>Organisation Service: Plan Activation Successful
```

### B. Agent Interaction & Credit Deduction
*Credits are deducted from the organisation's balance after successful agent interaction.*
```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Agent Service
    participant Payment Service
    participant User Service
    participant Analytics Service

    Client->>API Gateway: Agent Chat Request (user_id, org_id, agent_id, prompt)
    API Gateway->>Agent Service: ProcessChat(user_id, org_id, agent_id, prompt)
    Agent Service->>Agent Service: Execute agent logic
    Agent Service->>Agent Service: Calculate input/output tokens
    Agent Service->>Agent Service: Convert tokens to credits_to_deduct (e.g., 1000 tokens = 1 credit)

    %% Credit Deduction Attempt
    Agent Service->>Payment Service: DeductCredits(org_id, user_id, agent_id, credits_to_deduct, token_details)
    Payment Service->>User Service: GetOrganisationCreditBalance(org_id)
    User Service-->>Payment Service: current_balance
    alt Sufficient Credits (current_balance >= credits_to_deduct)
        Payment Service->>User Service: UpdateOrganisationCreditBalance(org_id, new_balance = current_balance - credits_to_deduct)
        User Service->>User Service: Update org.credit_balance in DB
        User Service-->>Payment Service: Balance Updated Successfully
        Payment Service->>Payment Service: Log to CreditLedger (type: USAGE_DEDUCTION, amount: -credits_to_deduct)
        Payment Service-->>Agent Service: Deduction Successful
        Agent Service->>Analytics Service: RecordUsageEvent(user_id, org_id, agent_id, tokens_used, credits_deducted, status="success")
        Analytics Service-->>Agent Service: Logged
        Agent Service-->>API Gateway: Chat Response
        API Gateway-->>Client: Chat Response
    else Insufficient Credits
        Payment Service-->>Agent Service: Deduction Failed (Reason: Insufficient Credits)
        Agent Service->>Analytics Service: RecordUsageEvent(user_id, org_id, agent_id, tokens_used, credits_attempted=credits_to_deduct, status="failed_insufficient_credits")
        Analytics Service-->>Agent Service: Logged
        Agent Service-->>API Gateway: Error: Insufficient Credits. Request not processed.
        API Gateway-->>Client: Error: Insufficient Credits. Please contact your organisation admin.
    end
```

### C. Pro Plan Upgrade by Organisation Admin
*Organisation admin upgrades to the Pro Plan via Stripe.*
```mermaid
sequenceDiagram
    participant Org Admin (Client)
    participant API Gateway
    participant Payment Service
    participant User Service
    participant Stripe

    Org Admin (Client)->>API Gateway: Request Pro Plan Upgrade (org_id)
    API Gateway->>Payment Service: CreateProPlanCheckoutSession(org_id, user_id_admin)
    Payment Service->>User Service: FetchStripeCustomerId(user_id_admin) # Or org_id if customer is org-level
    User Service-->>Payment Service: stripe_customer_id (if exists)
    Payment Service->>Stripe: Create Checkout Session (Pro Plan Price ID, customer_id?)
    Stripe-->>Payment Service: checkout_session_url
    Payment Service-->>API Gateway: checkout_session_url
    API Gateway-->>Org Admin (Client): Redirect to Stripe Checkout

    Org Admin (Client)->>Stripe: Completes Payment on Stripe
    Stripe-->>Payment Service: Webhook: checkout.session.completed (org_id, user_id_admin, plan_id="pro", stripe_customer_id)

    Payment Service->>Payment Service: Process Webhook (Verify signature)
    Payment Service->>User Service: UpdateStripeCustomerId(user_id_admin, stripe_customer_id) # If new/updated
    User Service-->>Payment Service: Customer ID Updated
    Payment Service->>User Service: AssignPlanToOrganisation(org_id, plan_id="pro")
    User Service-->>Payment Service: Plan Assigned
    Payment Service->>Payment Service: Retrieve "Pro Plan" details (100 credits)
    Payment Service->>User Service: UpdateOrganisationCreditBalance(org_id, amount_to_add=100) # Or set to 100 if replacing
    User Service-->>Payment Service: Credits Updated
    Payment Service->>Payment Service: Log to CreditLedger (type: PLAN_PURCHASE, amount: +100, notes: "Pro Plan Activation")
    Payment Service-->>Stripe: Acknowledge Webhook
## 5. Service-Specific Changes

#### A. `user-service`
*   **Data Model (Organisation Entity)**:
    *   `current_plan_id`: String (e.g., "free", "pro").
    *   `credit_balance`: Numeric (stores the current available credits for the organisation).
    *   `stripe_customer_id`: String (stores Stripe Customer ID, potentially linked to the org admin or the org itself).
*   **New/Updated gRPC Endpoints**:
    *   `SetInitialOrganisationCredits(org_id, amount)`: Sets `credit_balance`. Used for initial Free plan.
    *   `UpdateOrganisationCreditBalance(org_id, amount_to_change)`: Atomically updates `credit_balance` (positive for additions, negative for deductions). Returns new balance.
    *   `GetOrganisationCreditBalance(org_id)`: Returns `current_balance`.
    *   `AssignPlanToOrganisation(org_id, plan_id)`: Updates `current_plan_id`.
    *   `UpdateStripeCustomerId(user_id_or_org_id, stripe_customer_id)`: Stores/updates Stripe customer ID.
    *   `FetchStripeCustomerId(user_id_or_org_id)`: Retrieves Stripe customer ID.

#### B. `organisation-service`
*   **Logic Changes**:
    *   In `CreateOrganisation` gRPC method, after successful org creation:
        *   Invoke `payment-service.ActivateDefaultPlan(org_id, default_plan_id="free")`.

#### C. `agent-service` (or equivalent usage-metering service)
*   **Logic Changes**:
    *   Implement robust input/output token calculation per agent interaction.
    *   Implement token-to-credit conversion (rate: 1000 tokens = 1 credit, configurable).
    *   On interaction completion:
        1.  Call `payment-service.DeductCredits(org_id, user_id, credits_to_deduct, reference_details)`.
        2.  If `DeductCredits` fails due to insufficient credits, block agent response/further processing and return an appropriate error to the client.
        3.  Call `analytics-service.RecordUsageEvent` with comprehensive details (tokens, credits, status).

#### D. `payment-service`
*   **New Data Models (Database)**:
    *   `Plans`: `plan_id` (PK), `name`, `credit_amount`, `stripe_price_id` (for Pro/paid plans), `is_default` (boolean).
        *   Seed with: ("free", "Free Plan", 20, null, true), ("pro", "Pro Plan", 100, "stripe_price_id_for_pro", false).
    *   `CreditLedger`: `transaction_id` (PK), `organisation_id`, `user_id` (optional, actor), `timestamp`, `transaction_type` (enum: `INITIAL_ALLOCATION`, `USAGE_DEDUCTION`, `PLAN_PURCHASE`, `PLAN_CHANGE_ADJUSTMENT`, `MANUAL_ADJUSTMENT`), `amount` (decimal, signed), `balance_after_transaction`, `reference_id` (e.g., agent_interaction_id, stripe_charge_id), `notes`.
*   **New gRPC Endpoints**:
    *   `ActivateDefaultPlan(org_id, plan_id)`: Assigns plan and initial credits via `user-service`, logs to `CreditLedger`.
    *   `DeductCredits(org_id, user_id, credits_to_deduct, reference_details)`: Checks balance via `user-service`, updates balance via `user-service` if sufficient, logs to `CreditLedger`. Returns success/failure (insufficient credits).
    *   `CreateProPlanCheckoutSession(org_id, user_id_admin)`: Creates a Stripe Checkout session for Pro plan upgrade.
    *   `HandleStripeWebhook(payload, signature)`: Processes incoming Stripe webhooks (e.g., `checkout.session.completed`, `invoice.payment_succeeded`) to update plans and credits.
*   **New REST API Endpoints (for admin/monitoring)**:
    *   `GET /organisations/{org_id}/credit-ledger`: Returns paginated credit transaction history for an organisation.
*   **Stripe Integration**:
    *   Securely manage Stripe API keys and webhook secrets.
    *   Implement logic for creating Stripe Checkout Sessions.
    *   Implement robust Stripe webhook handling, including signature verification and idempotency.

#### E. `api-gateway`
*   **Routing**:
    *   Route new `payment-service` REST APIs (e.g., for credit ledger view).
    *   Route requests for Pro Plan upgrade to `payment-service.CreateProPlanCheckoutSession`.
    *   Expose Stripe webhook endpoint for `payment-service`.

#### F. `analytics-service`
*   **Usage Event Enhancement**:
    *   Ensure `RecordUsageEvent` or `RecordApiRequest` captures `organisation_id`, `input_tokens`, `output_tokens`, `credits_attempted`, `credits_deducted`, and `status_of_deduction`.

## 6. Data Models (Conceptual Summary)
*   **User Service (Organisation Entity)**: `id`, `name`, `owner_id`, `current_plan_id`, `credit_balance`, `stripe_customer_id`.
*   **Payment Service (DB)**:
    *   `Plan`: `plan_id`, `name`, `credit_amount`, `stripe_price_id`, `is_default`.
    *   `CreditLedger`: `transaction_id`, `organisation_id`, `user_id`, `timestamp`, `transaction_type`, `amount`, `balance_after_transaction`, `reference_id`, `notes`.
*   **Analytics Service (Event Data)**:
    *   `UsageEvent`: `id`, `user_id`, `organisation_id`, `entity_type` (e.g., "agent"), `entity_id`, `action`, `credits_used`, `event_metadata` (e.g., `{ "input_tokens": X, "output_tokens": Y, "deduction_status": "success/failed_insufficient_credits" }`).

## 7. Key Assumptions
*   **Token-to-Credit Conversion Rate**: 1000 tokens = 1 credit. This is configurable within `agent-service` or `payment-service`.
*   **Stripe Setup**: Stripe account, products (Pro Plan), and prices are configured.
*   **Admin Responsibility**: Organisation admins are responsible for initiating plan upgrades and payments.
*   **Real-time Deduction**: Credit deduction is attempted in real-time per usage event.
*   **Service Communication**: gRPC is the primary mode for inter-service communication.

## 8. Edge Cases & Considerations
*   **Concurrent Deductions**: Implement optimistic locking or atomic operations in `user-service` when updating `credit_balance` to handle concurrent requests from the same organisation.
*   **Stripe Webhook Failures/Retries**: Ensure robust error handling and retry mechanisms for Stripe webhook processing. Log failures.
*   **Plan Activation Failures**: If `organisation-service` creates an org but `payment-service` fails to activate the default plan (e.g., `user-service` is down), implement a retry mechanism or an admin alert.
*   **Data Consistency**: Eventual consistency between `user-service` (live balance) and `payment-service` (`CreditLedger`) is acceptable, but strive for minimal delay. The ledger is the historical record.
*   **Org Admin Change**: If an org admin changes, ensure Stripe customer details and payment responsibilities are handled correctly (future consideration, may involve manual Stripe updates initially).
*   **Zero Credit Cost Operations**: Some operations might not consume credits. Ensure this is handled.
*   **Manual Credit Adjustments**: Need for an admin interface/API in `payment-service` to manually adjust an organisation's credits (e.g., for goodwill, corrections), which would also log to `CreditLedger`.

## 9. Future Considerations
*   **Credit Top-ups**: Allow organisations to purchase additional credits outside of plan upgrades.
*   **Multiple Paid Plans**: Introduce more tiered plans.
*   **Notifications**: Low credit balance warnings, successful payment/upgrade confirmations.
*   **Plan Downgrades/Cancellations**.
*   **Refunds Processing**.

## 10. Resolved Questions (from previous discussions)
*   **Credit Tracking**: `payment-service` will maintain `CreditLedger`; `user-service` holds live `credit_balance`.
*   **Insufficient Credits**: Requests will be blocked.
*   **Pro Plan**: Upgradable, handled by `payment-service` via Stripe.
*   **Token Conversion**: Assumed 1000 tokens = 1 credit.
*   **Notification of Token Consumption**: `agent-service` calls `payment-service.DeductCredits`.
*   **User Consumption Data Structure**: `analytics-service` for detailed logs, `payment-service.CreditLedger` for financial transactions.
*   **Consumption Data Update Frequency**: Real-time deduction.