# Payment Service Tasks

This file tracks the implementation progress for the `payment-service`.

## Completed Tasks

- [x] **Database Schema Consolidation**
    - [x] Consolidated all payment-related tables into `payment_models.py`.
    - [x] Prefixed all table names with `payment_`.
    - [x] Merged `CreditLedger` and `Transaction` into a single `payment_transactions` table.
    - [x] Created a new `payment_subscriptions` table for managing organization subscriptions.

- [x] **Service Layer Consolidation**
    - [x] Consolidated all service logic into a single `payment_service.py` file.
    - [x] Implemented a function to deduce credits based on input and output tokens.
    - [x] Added a token-to-credit calculator.

- [x] **gRPC API Update**
    - [x] Updated `payment.proto` to reflect the new models and service structure.
    - [x] Removed `user.proto` and merged necessary components into `payment.proto`.
    - [x] Regenerated gRPC code from the updated proto file.
    - [x] Implemented all gRPC services in `payment_service.py`.

- [x] **Stripe Integration**
    - [x] Configured Stripe API keys and webhook signing secret.
    - [x] Implemented webhook handling for key events (`checkout.session.completed`, `invoice.paid`, `customer.subscription.deleted`).

## Pending Tasks

- [ ] **API Gateway Update**
    - [ ] Update the API gateway to use the new `payment-service` gRPC API.
    - [ ] Create new routes in the API gateway for the new subscription management and credit deduction functionalities.