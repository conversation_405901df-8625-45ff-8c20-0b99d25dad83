# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: user.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'user.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nuser.proto\x12\x04user\"D\n\x0fRegisterRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x10\n\x08\x66ullName\x18\x03 \x01(\t\"4\n\x10RegisterResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"/\n\x1eUpdateEmailVerificationDetails\x12\r\n\x05token\x18\x01 \x01(\t\"c\n\"UpdateEmailVerifiedDetailsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\"A\n\x0cLoginRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x10\n\x08\x66\x63mToken\x18\x03 \x01(\t\"\xab\x01\n\rLoginResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x13\n\x0b\x61\x63\x63\x65ssToken\x18\x03 \x01(\t\x12\x14\n\x0crefreshToken\x18\x04 \x01(\t\x12\x1c\n\x04user\x18\x05 \x01(\x0b\x32\x0e.user.UserInfo\x12\x16\n\x0e\x61\x63\x63\x65ssTokenAge\x18\x06 \x01(\x03\x12\x17\n\x0frefreshTokenAge\x18\x07 \x01(\x03\"r\n\x12GoogleOAuthRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x10\n\x08\x66ullName\x18\x02 \x01(\t\x12\x10\n\x08googleId\x18\x03 \x01(\t\x12\x13\n\x0b\x61\x63\x63\x65ssToken\x18\x04 \x01(\t\x12\x14\n\x0crefreshToken\x18\x05 \x01(\t\"+\n\x13RefreshTokenRequest\x12\x14\n\x0crefreshToken\x18\x01 \x01(\t\"A\n\x14RefreshTokenResponse\x12\x13\n\x0b\x61\x63\x63\x65ssToken\x18\x01 \x01(\t\x12\x14\n\x0crefreshToken\x18\x02 \x01(\t\"*\n\x12\x41\x63\x63\x65ssTokenRequest\x12\x14\n\x0crefreshToken\x18\x01 \x01(\t\"j\n\x13\x41\x63\x63\x65ssTokenResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x13\n\x0b\x61\x63\x63\x65ssToken\x18\x02 \x01(\t\x12\x15\n\rtokenExpireAt\x18\x03 \x01(\t\x12\x16\n\x0e\x61\x63\x63\x65ssTokenAge\x18\x04 \x01(\x03\"(\n\x17ResetPasswordOTPRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\"<\n\x18ResetPasswordOTPResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"W\n\x15UpdatePasswordRequest\x12\r\n\x05token\x18\x01 \x01(\t\x12\x13\n\x0bnewPassword\x18\x02 \x01(\t\x12\x1a\n\x12\x43onfirmNewPassword\x18\x03 \x01(\t\":\n\x16UpdatePasswordResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"p\n\x14ResetPasswordRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\x12\x17\n\x0f\x63urrentPassword\x18\x02 \x01(\t\x12\x13\n\x0bnewPassword\x18\x03 \x01(\t\x12\x1a\n\x12\x63onfirmNewPassword\x18\x04 \x01(\t\"9\n\x15ResetPasswordResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\" \n\x0eGetUserRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\"N\n\x0cUserResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1c\n\x04user\x18\x03 \x01(\x0b\x32\x0e.user.UserInfo\"\xdf\x01\n\x11UpdateUserRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\x12\x15\n\x08\x66ullName\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x12\n\x05\x65mail\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x15\n\x08password\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\x18\n\x0bphoneNumber\x18\x05 \x01(\tH\x03\x88\x01\x01\x12\x19\n\x0cprofileImage\x18\x06 \x01(\tH\x04\x88\x01\x01\x42\x0b\n\t_fullNameB\x08\n\x06_emailB\x0b\n\t_passwordB\x0e\n\x0c_phoneNumberB\x0f\n\r_profileImage\"#\n\x11\x44\x65leteUserRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\"6\n\x12\x44\x65leteUserResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"2\n\x10ListUsersRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x10\n\x08pageSize\x18\x02 \x01(\x05\"C\n\x12SearchUsersRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x10\n\x08pageSize\x18\x03 \x01(\x05\"c\n\x11ListUsersResponse\x12\x1d\n\x05users\x18\x01 \x03(\x0b\x32\x0e.user.UserInfo\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x12\n\ntotalPages\x18\x04 \x01(\x05\"\x81\x02\n\x08UserInfo\x12\x0e\n\x06userId\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x10\n\x08\x66ullName\x18\x03 \x01(\t\x12\x11\n\tcreatedAt\x18\x04 \x01(\t\x12\x11\n\tupdatedAt\x18\x05 \x01(\t\x12\x0c\n\x04role\x18\x06 \x01(\t\x12\x0f\n\x07\x63ompany\x18\x07 \x01(\t\x12\x12\n\ndepartment\x18\x08 \x01(\t\x12\x0f\n\x07jobRole\x18\t \x01(\t\x12\x13\n\x0bphoneNumber\x18\n \x01(\t\x12\x14\n\x0cprofileImage\x18\x0b \x01(\t\x12\x14\n\x0cisFirstLogin\x18\x0c \x01(\x08\x12\x19\n\x11githubAccessToken\x18\r \x01(\t\"\x8c\x02\n\x12GetAllUsersRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x10\n\x08pageSize\x18\x02 \x01(\x05\x12\x13\n\x06sortBy\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x16\n\tsortOrder\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x1c\n\x0fisEmailVerified\x18\x05 \x01(\x08H\x02\x88\x01\x01\x12\x11\n\x04role\x18\x06 \x01(\tH\x03\x88\x01\x01\x12\x15\n\x08isActive\x18\x07 \x01(\x08H\x04\x88\x01\x01\x12\x13\n\x06search\x18\x08 \x01(\tH\x05\x88\x01\x01\x42\t\n\x07_sortByB\x0c\n\n_sortOrderB\x12\n\x10_isEmailVerifiedB\x07\n\x05_roleB\x0b\n\t_isActiveB\t\n\x07_search\"\xd2\x01\n\x13GetAllUsersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1d\n\x05users\x18\x03 \x03(\x0b\x32\x0e.user.UserInfo\x12(\n\npagination\x18\x04 \x01(\x0b\x32\x14.user.PaginationInfo\x12\"\n\x07sorting\x18\x05 \x01(\x0b\x32\x11.user.SortingInfo\x12,\n\x0csearchFilter\x18\x06 \x01(\x0b\x32\x16.user.SearchFilterInfo\"_\n\x0ePaginationInfo\x12\x13\n\x0b\x63urrentPage\x18\x01 \x01(\x05\x12\x12\n\ntotalPages\x18\x02 \x01(\x05\x12\x12\n\ntotalItems\x18\x03 \x01(\x05\x12\x10\n\x08pageSize\x18\x04 \x01(\x05\"0\n\x0bSortingInfo\x12\x0e\n\x06sortBy\x18\x01 \x01(\t\x12\x11\n\tsortOrder\x18\x02 \x01(\t\">\n\x10SearchFilterInfo\x12\x16\n\x0e\x61ppliedFilters\x18\x01 \x01(\t\x12\x12\n\nsearchTerm\x18\x02 \x01(\t\"\x9d\x01\n\x1fUpdateUserProfileDetailsRequest\x12\x0e\n\x06userId\x18\x01 \x01(\t\x12\x14\n\x07\x63ompany\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x17\n\ndepartment\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x14\n\x07jobRole\x18\x04 \x01(\tH\x02\x88\x01\x01\x42\n\n\x08_companyB\r\n\x0b_departmentB\n\n\x08_jobRole\"\xa9\x01\n\x10OrganizationInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0f\n\x07ownerId\x18\x04 \x01(\t\x12\x0f\n\x07userIds\x18\x05 \x03(\t\x12\x11\n\tcreatedAt\x18\x06 \x01(\t\x12\x11\n\tupdatedAt\x18\x07 \x01(\t\x12\x1d\n\x05owner\x18\x08 \x01(\x0b\x32\x0e.user.UserInfo\"X\n\x19\x43reateOrganizationRequest\x12\r\n\x05title\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x17\n\x0frequesterUserId\x18\x03 \x01(\t\"f\n\x14OrganizationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12,\n\x0corganization\x18\x03 \x01(\x0b\x32\x16.user.OrganizationInfo\"I\n\x16GetOrganizationRequest\x12\x16\n\x0eorganizationId\x18\x01 \x01(\t\x12\x17\n\x0frequesterUserId\x18\x02 \x01(\t\"\x94\x01\n\x19UpdateOrganizationRequest\x12\x16\n\x0eorganizationId\x18\x01 \x01(\t\x12\x12\n\x05title\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x17\n\x0frequesterUserId\x18\x04 \x01(\tB\x08\n\x06_titleB\x0e\n\x0c_description\"L\n\x19\x44\x65leteOrganizationRequest\x12\x16\n\x0eorganizationId\x18\x01 \x01(\t\x12\x17\n\x0frequesterUserId\x18\x02 \x01(\t\">\n\x1a\x44\x65leteOrganizationResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"d\n\x1c\x41\x64\x64UserToOrganizationRequest\x12\x16\n\x0eorganizationId\x18\x01 \x01(\t\x12\x13\n\x0buserIdToAdd\x18\x02 \x01(\t\x12\x17\n\x0frequesterUserId\x18\x03 \x01(\t\"l\n!RemoveUserFromOrganizationRequest\x12\x16\n\x0eorganizationId\x18\x01 \x01(\t\x12\x16\n\x0euserIdToRemove\x18\x02 \x01(\t\x12\x17\n\x0frequesterUserId\x18\x03 \x01(\t\"7\n\x1cListUserOrganizationsRequest\x12\x17\n\x0frequesterUserId\x18\x01 \x01(\t\"l\n\x19ListOrganizationsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12-\n\rorganizations\x18\x03 \x03(\x0b\x32\x16.user.OrganizationInfo\"%\n\x14\x41\x64\x64ToWaitlistRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\"9\n\x15\x41\x64\x64ToWaitlistResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"M\n\rWaitlistEntry\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x11\n\tjoined_at\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\"L\n\x12GetWaitlistRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x15\n\rstatus_filter\x18\x03 \x01(\t\"m\n\x13GetWaitlistResponse\x12$\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\x13.user.WaitlistEntry\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x04 \x01(\x05\"(\n\x1a\x41pproveWaitlistUserRequest\x12\n\n\x02id\x18\x01 \x01(\t\"?\n\x1b\x41pproveWaitlistUserResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"2\n#ApproveMultipleWaitlistUsersRequest\x12\x0b\n\x03ids\x18\x01 \x03(\t\"t\n$ApproveMultipleWaitlistUsersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x16\n\x0e\x61pproved_count\x18\x03 \x01(\x05\x12\x12\n\nfailed_ids\x18\x04 \x03(\t\"L\n\x1dUpdateStripeCustomerIdRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x1a\n\x12stripe_customer_id\x18\x02 \x01(\t\"B\n\x1eUpdateStripeCustomerIdResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"/\n\x1c\x46\x65tchStripeCustomerIdRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\";\n\x1d\x46\x65tchStripeCustomerIdResponse\x12\x1a\n\x12stripe_customer_id\x18\x01 \x01(\t\"N\n\x0bUserDetails\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x11\n\tfull_name\x18\x03 \x01(\t\x12\x11\n\tfcm_token\x18\x04 \x01(\t\"!\n\x13ValidateUserRequest\x12\n\n\x02id\x18\x01 \x01(\t\"Y\n\x14ValidateUserResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1f\n\x04user\x18\x03 \x01(\x0b\x32\x11.user.UserDetails\"(\n\x14GetUsersByIdsRequest\x12\x10\n\x08user_ids\x18\x01 \x03(\t\"[\n\x15GetUsersByIdsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12 \n\x05users\x18\x03 \x03(\x0b\x32\x11.user.UserDetails\";\n\x14GetAPIKeyByIdRequest\x12\x12\n\napi_key_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"G\n\x15GenerateAPIKeyRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07project\x18\x03 \x01(\t\"c\n\x16GenerateAPIKeyResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\npublic_key\x18\x03 \x01(\t\x12\x13\n\x0bprivate_key\x18\x04 \x01(\t\"%\n\x12ListAPIKeysRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"6\n\x13\x44\x65leteAPIKeyRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0e\n\x06key_id\x18\x02 \x01(\t\"8\n\x14\x44\x65leteAPIKeyResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"t\n\nAPIKeyInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\npublic_key\x18\x03 \x01(\t\x12\x13\n\x0bprivate_key\x18\x04 \x01(\t\x12\x0f\n\x07project\x18\x05 \x01(\t\x12\x12\n\ncreated_at\x18\x06 \x01(\t\"[\n\x13ListAPIKeysResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\"\n\x08\x61pi_keys\x18\x03 \x03(\x0b\x32\x10.user.APIKeyInfo\"\\\n\x15GetAPIKeyByIdResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12!\n\x07\x61pi_key\x18\x03 \x01(\x0b\x32\x10.user.APIKeyInfo\"v\n\x17\x43reateCredentialRequest\x12\x10\n\x08key_name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\x10\n\x08owner_id\x18\x03 \x01(\t\x12\x18\n\x0b\x64\x65scription\x18\x04 \x01(\tH\x00\x88\x01\x01\x42\x0e\n\x0c_description\"Z\n\x18\x43reateCredentialResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\n\n\x02id\x18\x03 \x01(\t\x12\x10\n\x08key_name\x18\x04 \x01(\t\"?\n\x14GetCredentialRequest\x12\x15\n\rcredential_id\x18\x01 \x01(\t\x12\x10\n\x08owner_id\x18\x02 \x01(\t\"c\n\x15GetCredentialResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12(\n\ncredential\x18\x03 \x01(\x0b\x32\x14.user.CredentialInfo\"*\n\x16ListCredentialsRequest\x12\x10\n\x08owner_id\x18\x01 \x01(\t\"f\n\x17ListCredentialsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x0b\x63redentials\x18\x03 \x03(\x0b\x32\x14.user.CredentialInfo\"B\n\x17\x44\x65leteCredentialRequest\x12\x15\n\rcredential_id\x18\x01 \x01(\t\x12\x10\n\x08owner_id\x18\x02 \x01(\t\"<\n\x18\x44\x65leteCredentialResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x90\x01\n\x0e\x43redentialInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08key_name\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x12\n\ncreated_at\x18\x05 \x01(\t\x12\x14\n\x0clast_used_at\x18\x06 \x01(\t\x12\x12\n\nupdated_at\x18\x07 \x01(\t\"\xae\x01\n\x17UpdateCredentialRequest\x12\x15\n\rcredential_id\x18\x01 \x01(\t\x12\x10\n\x08owner_id\x18\x02 \x01(\t\x12\x15\n\x08key_name\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x12\n\x05value\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x05 \x01(\tH\x02\x88\x01\x01\x42\x0b\n\t_key_nameB\x08\n\x06_valueB\x0e\n\x0c_description\"Z\n\x18UpdateCredentialResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\n\n\x02id\x18\x03 \x01(\t\x12\x10\n\x08key_name\x18\x04 \x01(\t\"B\n\x12UpdateUserGitToken\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x1b\n\x13github_access_token\x18\x02 \x01(\t\",\n\x19GetUserGitHubTokenRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"T\n\x1aGetUserGitHubTokenResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x03 \x01(\t2\xe5\x19\n\x0bUserService\x12\x39\n\x08register\x12\x15.user.RegisterRequest\x1a\x16.user.RegisterResponse\x12l\n\x1aupdateEmailVerifiedDetails\x12$.user.UpdateEmailVerificationDetails\x1a(.user.UpdateEmailVerifiedDetailsResponse\x12\x30\n\x05login\x12\x12.user.LoginRequest\x1a\x13.user.LoginResponse\x12\x41\n\x10googleOAuthLogin\x12\x18.user.GoogleOAuthRequest\x1a\x13.user.LoginResponse\x12\x45\n\x0crefreshToken\x12\x19.user.RefreshTokenRequest\x1a\x1a.user.RefreshTokenResponse\x12\x42\n\x0b\x61\x63\x63\x65ssToken\x12\x18.user.AccessTokenRequest\x1a\x19.user.AccessTokenResponse\x12Y\n\x18generateResetPasswordOTP\x12\x1d.user.ResetPasswordOTPRequest\x1a\x1e.user.ResetPasswordOTPResponse\x12K\n\x0eupdatePassword\x12\x1b.user.UpdatePasswordRequest\x1a\x1c.user.UpdatePasswordResponse\x12H\n\rresetPassword\x12\x1a.user.ResetPasswordRequest\x1a\x1b.user.ResetPasswordResponse\x12U\n\x18updateUserProfileDetails\x12%.user.UpdateUserProfileDetailsRequest\x1a\x12.user.UserResponse\x12\x33\n\x07getUser\x12\x14.user.GetUserRequest\x1a\x12.user.UserResponse\x12\x39\n\nupdateUser\x12\x17.user.UpdateUserRequest\x1a\x12.user.UserResponse\x12?\n\ndeleteUser\x12\x17.user.DeleteUserRequest\x1a\x18.user.DeleteUserResponse\x12<\n\tlistUsers\x12\x16.user.ListUsersRequest\x1a\x17.user.ListUsersResponse\x12@\n\x0bsearchUsers\x12\x18.user.SearchUsersRequest\x1a\x17.user.ListUsersResponse\x12\x63\n\x16updateStripeCustomerId\x12#.user.UpdateStripeCustomerIdRequest\x1a$.user.UpdateStripeCustomerIdResponse\x12`\n\x15\x66\x65tchStripeCustomerId\x12\".user.FetchStripeCustomerIdRequest\x1a#.user.FetchStripeCustomerIdResponse\x12H\n\raddToWaitlist\x12\x1a.user.AddToWaitlistRequest\x1a\x1b.user.AddToWaitlistResponse\x12\x42\n\x0bgetWaitlist\x12\x18.user.GetWaitlistRequest\x1a\x19.user.GetWaitlistResponse\x12Z\n\x13\x61pproveWaitlistUser\x12 .user.ApproveWaitlistUserRequest\x1a!.user.ApproveWaitlistUserResponse\x12u\n\x1c\x61pproveMultipleWaitlistUsers\x12).user.ApproveMultipleWaitlistUsersRequest\x1a*.user.ApproveMultipleWaitlistUsersResponse\x12\x45\n\x0cvalidateUser\x12\x19.user.ValidateUserRequest\x1a\x1a.user.ValidateUserResponse\x12H\n\rgetUsersByIds\x12\x1a.user.GetUsersByIdsRequest\x1a\x1b.user.GetUsersByIdsResponse\x12K\n\x0eGenerateAPIKey\x12\x1b.user.GenerateAPIKeyRequest\x1a\x1c.user.GenerateAPIKeyResponse\x12\x42\n\x0bListAPIKeys\x12\x18.user.ListAPIKeysRequest\x1a\x19.user.ListAPIKeysResponse\x12\x45\n\x0c\x44\x65leteAPIKey\x12\x19.user.DeleteAPIKeyRequest\x1a\x1a.user.DeleteAPIKeyResponse\x12H\n\rGetAPIKeyById\x12\x1a.user.GetAPIKeyByIdRequest\x1a\x1b.user.GetAPIKeyByIdResponse\x12\x42\n\x0bgetAllUsers\x12\x18.user.GetAllUsersRequest\x1a\x19.user.GetAllUsersResponse\x12Q\n\x12\x63reateOrganization\x12\x1f.user.CreateOrganizationRequest\x1a\x1a.user.OrganizationResponse\x12K\n\x0fgetOrganization\x12\x1c.user.GetOrganizationRequest\x1a\x1a.user.OrganizationResponse\x12Q\n\x12updateOrganization\x12\x1f.user.UpdateOrganizationRequest\x1a\x1a.user.OrganizationResponse\x12W\n\x12\x64\x65leteOrganization\x12\x1f.user.DeleteOrganizationRequest\x1a .user.DeleteOrganizationResponse\x12W\n\x15\x61\x64\x64UserToOrganization\x12\".user.AddUserToOrganizationRequest\x1a\x1a.user.OrganizationResponse\x12\x61\n\x1aremoveUserFromOrganization\x12\'.user.RemoveUserFromOrganizationRequest\x1a\x1a.user.OrganizationResponse\x12\\\n\x15listUserOrganizations\x12\".user.ListUserOrganizationsRequest\x1a\x1f.user.ListOrganizationsResponse\x12Q\n\x10\x43reateCredential\x12\x1d.user.CreateCredentialRequest\x1a\x1e.user.CreateCredentialResponse\x12H\n\rGetCredential\x12\x1a.user.GetCredentialRequest\x1a\x1b.user.GetCredentialResponse\x12N\n\x0fListCredentials\x12\x1c.user.ListCredentialsRequest\x1a\x1d.user.ListCredentialsResponse\x12Q\n\x10\x44\x65leteCredential\x12\x1d.user.DeleteCredentialRequest\x1a\x1e.user.DeleteCredentialResponse\x12Q\n\x10UpdateCredential\x12\x1d.user.UpdateCredentialRequest\x1a\x1e.user.UpdateCredentialResponse\x12\x42\n\x12updateUserGitToken\x12\x18.user.UpdateUserGitToken\x1a\x12.user.UserResponse\x12W\n\x12getUserGitHubToken\x12\x1f.user.GetUserGitHubTokenRequest\x1a .user.GetUserGitHubTokenResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'user_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_REGISTERREQUEST']._serialized_start=20
  _globals['_REGISTERREQUEST']._serialized_end=88
  _globals['_REGISTERRESPONSE']._serialized_start=90
  _globals['_REGISTERRESPONSE']._serialized_end=142
  _globals['_UPDATEEMAILVERIFICATIONDETAILS']._serialized_start=144
  _globals['_UPDATEEMAILVERIFICATIONDETAILS']._serialized_end=191
  _globals['_UPDATEEMAILVERIFIEDDETAILSRESPONSE']._serialized_start=193
  _globals['_UPDATEEMAILVERIFIEDDETAILSRESPONSE']._serialized_end=292
  _globals['_LOGINREQUEST']._serialized_start=294
  _globals['_LOGINREQUEST']._serialized_end=359
  _globals['_LOGINRESPONSE']._serialized_start=362
  _globals['_LOGINRESPONSE']._serialized_end=533
  _globals['_GOOGLEOAUTHREQUEST']._serialized_start=535
  _globals['_GOOGLEOAUTHREQUEST']._serialized_end=649
  _globals['_REFRESHTOKENREQUEST']._serialized_start=651
  _globals['_REFRESHTOKENREQUEST']._serialized_end=694
  _globals['_REFRESHTOKENRESPONSE']._serialized_start=696
  _globals['_REFRESHTOKENRESPONSE']._serialized_end=761
  _globals['_ACCESSTOKENREQUEST']._serialized_start=763
  _globals['_ACCESSTOKENREQUEST']._serialized_end=805
  _globals['_ACCESSTOKENRESPONSE']._serialized_start=807
  _globals['_ACCESSTOKENRESPONSE']._serialized_end=913
  _globals['_RESETPASSWORDOTPREQUEST']._serialized_start=915
  _globals['_RESETPASSWORDOTPREQUEST']._serialized_end=955
  _globals['_RESETPASSWORDOTPRESPONSE']._serialized_start=957
  _globals['_RESETPASSWORDOTPRESPONSE']._serialized_end=1017
  _globals['_UPDATEPASSWORDREQUEST']._serialized_start=1019
  _globals['_UPDATEPASSWORDREQUEST']._serialized_end=1106
  _globals['_UPDATEPASSWORDRESPONSE']._serialized_start=1108
  _globals['_UPDATEPASSWORDRESPONSE']._serialized_end=1166
  _globals['_RESETPASSWORDREQUEST']._serialized_start=1168
  _globals['_RESETPASSWORDREQUEST']._serialized_end=1280
  _globals['_RESETPASSWORDRESPONSE']._serialized_start=1282
  _globals['_RESETPASSWORDRESPONSE']._serialized_end=1339
  _globals['_GETUSERREQUEST']._serialized_start=1341
  _globals['_GETUSERREQUEST']._serialized_end=1373
  _globals['_USERRESPONSE']._serialized_start=1375
  _globals['_USERRESPONSE']._serialized_end=1453
  _globals['_UPDATEUSERREQUEST']._serialized_start=1456
  _globals['_UPDATEUSERREQUEST']._serialized_end=1679
  _globals['_DELETEUSERREQUEST']._serialized_start=1681
  _globals['_DELETEUSERREQUEST']._serialized_end=1716
  _globals['_DELETEUSERRESPONSE']._serialized_start=1718
  _globals['_DELETEUSERRESPONSE']._serialized_end=1772
  _globals['_LISTUSERSREQUEST']._serialized_start=1774
  _globals['_LISTUSERSREQUEST']._serialized_end=1824
  _globals['_SEARCHUSERSREQUEST']._serialized_start=1826
  _globals['_SEARCHUSERSREQUEST']._serialized_end=1893
  _globals['_LISTUSERSRESPONSE']._serialized_start=1895
  _globals['_LISTUSERSRESPONSE']._serialized_end=1994
  _globals['_USERINFO']._serialized_start=1997
  _globals['_USERINFO']._serialized_end=2254
  _globals['_GETALLUSERSREQUEST']._serialized_start=2257
  _globals['_GETALLUSERSREQUEST']._serialized_end=2525
  _globals['_GETALLUSERSRESPONSE']._serialized_start=2528
  _globals['_GETALLUSERSRESPONSE']._serialized_end=2738
  _globals['_PAGINATIONINFO']._serialized_start=2740
  _globals['_PAGINATIONINFO']._serialized_end=2835
  _globals['_SORTINGINFO']._serialized_start=2837
  _globals['_SORTINGINFO']._serialized_end=2885
  _globals['_SEARCHFILTERINFO']._serialized_start=2887
  _globals['_SEARCHFILTERINFO']._serialized_end=2949
  _globals['_UPDATEUSERPROFILEDETAILSREQUEST']._serialized_start=2952
  _globals['_UPDATEUSERPROFILEDETAILSREQUEST']._serialized_end=3109
  _globals['_ORGANIZATIONINFO']._serialized_start=3112
  _globals['_ORGANIZATIONINFO']._serialized_end=3281
  _globals['_CREATEORGANIZATIONREQUEST']._serialized_start=3283
  _globals['_CREATEORGANIZATIONREQUEST']._serialized_end=3371
  _globals['_ORGANIZATIONRESPONSE']._serialized_start=3373
  _globals['_ORGANIZATIONRESPONSE']._serialized_end=3475
  _globals['_GETORGANIZATIONREQUEST']._serialized_start=3477
  _globals['_GETORGANIZATIONREQUEST']._serialized_end=3550
  _globals['_UPDATEORGANIZATIONREQUEST']._serialized_start=3553
  _globals['_UPDATEORGANIZATIONREQUEST']._serialized_end=3701
  _globals['_DELETEORGANIZATIONREQUEST']._serialized_start=3703
  _globals['_DELETEORGANIZATIONREQUEST']._serialized_end=3779
  _globals['_DELETEORGANIZATIONRESPONSE']._serialized_start=3781
  _globals['_DELETEORGANIZATIONRESPONSE']._serialized_end=3843
  _globals['_ADDUSERTOORGANIZATIONREQUEST']._serialized_start=3845
  _globals['_ADDUSERTOORGANIZATIONREQUEST']._serialized_end=3945
  _globals['_REMOVEUSERFROMORGANIZATIONREQUEST']._serialized_start=3947
  _globals['_REMOVEUSERFROMORGANIZATIONREQUEST']._serialized_end=4055
  _globals['_LISTUSERORGANIZATIONSREQUEST']._serialized_start=4057
  _globals['_LISTUSERORGANIZATIONSREQUEST']._serialized_end=4112
  _globals['_LISTORGANIZATIONSRESPONSE']._serialized_start=4114
  _globals['_LISTORGANIZATIONSRESPONSE']._serialized_end=4222
  _globals['_ADDTOWAITLISTREQUEST']._serialized_start=4224
  _globals['_ADDTOWAITLISTREQUEST']._serialized_end=4261
  _globals['_ADDTOWAITLISTRESPONSE']._serialized_start=4263
  _globals['_ADDTOWAITLISTRESPONSE']._serialized_end=4320
  _globals['_WAITLISTENTRY']._serialized_start=4322
  _globals['_WAITLISTENTRY']._serialized_end=4399
  _globals['_GETWAITLISTREQUEST']._serialized_start=4401
  _globals['_GETWAITLISTREQUEST']._serialized_end=4477
  _globals['_GETWAITLISTRESPONSE']._serialized_start=4479
  _globals['_GETWAITLISTRESPONSE']._serialized_end=4588
  _globals['_APPROVEWAITLISTUSERREQUEST']._serialized_start=4590
  _globals['_APPROVEWAITLISTUSERREQUEST']._serialized_end=4630
  _globals['_APPROVEWAITLISTUSERRESPONSE']._serialized_start=4632
  _globals['_APPROVEWAITLISTUSERRESPONSE']._serialized_end=4695
  _globals['_APPROVEMULTIPLEWAITLISTUSERSREQUEST']._serialized_start=4697
  _globals['_APPROVEMULTIPLEWAITLISTUSERSREQUEST']._serialized_end=4747
  _globals['_APPROVEMULTIPLEWAITLISTUSERSRESPONSE']._serialized_start=4749
  _globals['_APPROVEMULTIPLEWAITLISTUSERSRESPONSE']._serialized_end=4865
  _globals['_UPDATESTRIPECUSTOMERIDREQUEST']._serialized_start=4867
  _globals['_UPDATESTRIPECUSTOMERIDREQUEST']._serialized_end=4943
  _globals['_UPDATESTRIPECUSTOMERIDRESPONSE']._serialized_start=4945
  _globals['_UPDATESTRIPECUSTOMERIDRESPONSE']._serialized_end=5011
  _globals['_FETCHSTRIPECUSTOMERIDREQUEST']._serialized_start=5013
  _globals['_FETCHSTRIPECUSTOMERIDREQUEST']._serialized_end=5060
  _globals['_FETCHSTRIPECUSTOMERIDRESPONSE']._serialized_start=5062
  _globals['_FETCHSTRIPECUSTOMERIDRESPONSE']._serialized_end=5121
  _globals['_USERDETAILS']._serialized_start=5123
  _globals['_USERDETAILS']._serialized_end=5201
  _globals['_VALIDATEUSERREQUEST']._serialized_start=5203
  _globals['_VALIDATEUSERREQUEST']._serialized_end=5236
  _globals['_VALIDATEUSERRESPONSE']._serialized_start=5238
  _globals['_VALIDATEUSERRESPONSE']._serialized_end=5327
  _globals['_GETUSERSBYIDSREQUEST']._serialized_start=5329
  _globals['_GETUSERSBYIDSREQUEST']._serialized_end=5369
  _globals['_GETUSERSBYIDSRESPONSE']._serialized_start=5371
  _globals['_GETUSERSBYIDSRESPONSE']._serialized_end=5462
  _globals['_GETAPIKEYBYIDREQUEST']._serialized_start=5464
  _globals['_GETAPIKEYBYIDREQUEST']._serialized_end=5523
  _globals['_GENERATEAPIKEYREQUEST']._serialized_start=5525
  _globals['_GENERATEAPIKEYREQUEST']._serialized_end=5596
  _globals['_GENERATEAPIKEYRESPONSE']._serialized_start=5598
  _globals['_GENERATEAPIKEYRESPONSE']._serialized_end=5697
  _globals['_LISTAPIKEYSREQUEST']._serialized_start=5699
  _globals['_LISTAPIKEYSREQUEST']._serialized_end=5736
  _globals['_DELETEAPIKEYREQUEST']._serialized_start=5738
  _globals['_DELETEAPIKEYREQUEST']._serialized_end=5792
  _globals['_DELETEAPIKEYRESPONSE']._serialized_start=5794
  _globals['_DELETEAPIKEYRESPONSE']._serialized_end=5850
  _globals['_APIKEYINFO']._serialized_start=5852
  _globals['_APIKEYINFO']._serialized_end=5968
  _globals['_LISTAPIKEYSRESPONSE']._serialized_start=5970
  _globals['_LISTAPIKEYSRESPONSE']._serialized_end=6061
  _globals['_GETAPIKEYBYIDRESPONSE']._serialized_start=6063
  _globals['_GETAPIKEYBYIDRESPONSE']._serialized_end=6155
  _globals['_CREATECREDENTIALREQUEST']._serialized_start=6157
  _globals['_CREATECREDENTIALREQUEST']._serialized_end=6275
  _globals['_CREATECREDENTIALRESPONSE']._serialized_start=6277
  _globals['_CREATECREDENTIALRESPONSE']._serialized_end=6367
  _globals['_GETCREDENTIALREQUEST']._serialized_start=6369
  _globals['_GETCREDENTIALREQUEST']._serialized_end=6432
  _globals['_GETCREDENTIALRESPONSE']._serialized_start=6434
  _globals['_GETCREDENTIALRESPONSE']._serialized_end=6533
  _globals['_LISTCREDENTIALSREQUEST']._serialized_start=6535
  _globals['_LISTCREDENTIALSREQUEST']._serialized_end=6577
  _globals['_LISTCREDENTIALSRESPONSE']._serialized_start=6579
  _globals['_LISTCREDENTIALSRESPONSE']._serialized_end=6681
  _globals['_DELETECREDENTIALREQUEST']._serialized_start=6683
  _globals['_DELETECREDENTIALREQUEST']._serialized_end=6749
  _globals['_DELETECREDENTIALRESPONSE']._serialized_start=6751
  _globals['_DELETECREDENTIALRESPONSE']._serialized_end=6811
  _globals['_CREDENTIALINFO']._serialized_start=6814
  _globals['_CREDENTIALINFO']._serialized_end=6958
  _globals['_UPDATECREDENTIALREQUEST']._serialized_start=6961
  _globals['_UPDATECREDENTIALREQUEST']._serialized_end=7135
  _globals['_UPDATECREDENTIALRESPONSE']._serialized_start=7137
  _globals['_UPDATECREDENTIALRESPONSE']._serialized_end=7227
  _globals['_UPDATEUSERGITTOKEN']._serialized_start=7229
  _globals['_UPDATEUSERGITTOKEN']._serialized_end=7295
  _globals['_GETUSERGITHUBTOKENREQUEST']._serialized_start=7297
  _globals['_GETUSERGITHUBTOKENREQUEST']._serialized_end=7341
  _globals['_GETUSERGITHUBTOKENRESPONSE']._serialized_start=7343
  _globals['_GETUSERGITHUBTOKENRESPONSE']._serialized_end=7427
  _globals['_USERSERVICE']._serialized_start=7430
  _globals['_USERSERVICE']._serialized_end=10731
# @@protoc_insertion_point(module_scope)
