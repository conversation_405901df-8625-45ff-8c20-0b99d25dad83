# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: provider.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'provider.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0eprovider.proto\x12\x08provider\"\x1c\n\x0eGetByIdRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x1b\n\rDeleteRequest\x12\n\n\x02id\x18\x01 \x01(\t\"2\n\x0e\x44\x65leteResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"y\n\x0bListRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x10\n\x08pageSize\x18\x02 \x01(\x05\x12\x15\n\x08isActive\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12\x17\n\nproviderId\x18\x04 \x01(\tH\x01\x88\x01\x01\x42\x0b\n\t_isActiveB\r\n\x0b_providerId\"n\n\x14GetByProviderRequest\x12\x12\n\nproviderId\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x10\n\x08pageSize\x18\x03 \x01(\x05\x12\x15\n\x08isActive\x18\x04 \x01(\x08H\x00\x88\x01\x01\x42\x0b\n\t_isActive\"\xae\x01\n\x15\x43reateProviderRequest\x12\x10\n\x08provider\x18\x01 \x01(\t\x12\x18\n\x0b\x64\x65scription\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x07\x62\x61seUrl\x18\x03 \x01(\t\x12\x15\n\x08isActive\x18\x04 \x01(\x08H\x01\x88\x01\x01\x12\x16\n\tisDefault\x18\x05 \x01(\x08H\x02\x88\x01\x01\x42\x0e\n\x0c_descriptionB\x0b\n\t_isActiveB\x0c\n\n_isDefault\"\xdd\x01\n\x15UpdateProviderRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x15\n\x08provider\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x14\n\x07\x62\x61seUrl\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\x15\n\x08isActive\x18\x05 \x01(\x08H\x03\x88\x01\x01\x12\x16\n\tisDefault\x18\x06 \x01(\x08H\x04\x88\x01\x01\x42\x0b\n\t_providerB\x0e\n\x0c_descriptionB\n\n\x08_baseUrlB\x0b\n\t_isActiveB\x0c\n\n_isDefault\"p\n\x10ProviderResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12-\n\x08provider\x18\x03 \x01(\x0b\x32\x16.provider.ProviderInfoH\x00\x88\x01\x01\x42\x0b\n\t_provider\"\x92\x01\n\x15ListProvidersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\tproviders\x18\x03 \x03(\x0b\x32\x16.provider.ProviderInfo\x12,\n\npagination\x18\x04 \x01(\x0b\x32\x18.provider.PaginationInfo\"\xc6\x01\n\x0cProviderInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08provider\x18\x02 \x01(\t\x12\x18\n\x0b\x64\x65scription\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x07\x62\x61seUrl\x18\x04 \x01(\t\x12\x10\n\x08isActive\x18\x05 \x01(\x08\x12\x11\n\tisDefault\x18\x06 \x01(\x08\x12\x11\n\tcreatedAt\x18\x07 \x01(\t\x12\x11\n\tupdatedAt\x18\x08 \x01(\t\x12\x12\n\nmodelCount\x18\t \x01(\x05\x42\x0e\n\x0c_description\"\xe8\x02\n\x12\x43reateModelRequest\x12\x12\n\nproviderId\x18\x01 \x01(\t\x12\r\n\x05model\x18\x02 \x01(\t\x12\x0f\n\x07modelId\x18\x03 \x01(\t\x12\x18\n\x0b\x64\x65scription\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x1b\n\x0epricePerTokens\x18\x05 \x01(\x02H\x01\x88\x01\x01\x12\x16\n\tmaxTokens\x18\x06 \x01(\x05H\x02\x88\x01\x01\x12\x18\n\x0btemperature\x18\x07 \x01(\x02H\x03\x88\x01\x01\x12\x19\n\x0cproviderType\x18\x08 \x01(\tH\x04\x88\x01\x01\x12\x15\n\x08isActive\x18\t \x01(\x08H\x05\x88\x01\x01\x12\x16\n\tisDefault\x18\n \x01(\x08H\x06\x88\x01\x01\x42\x0e\n\x0c_descriptionB\x11\n\x0f_pricePerTokensB\x0c\n\n_maxTokensB\x0e\n\x0c_temperatureB\x0f\n\r_providerTypeB\x0b\n\t_isActiveB\x0c\n\n_isDefault\"\xa8\x03\n\x12UpdateModelRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\nproviderId\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x12\n\x05model\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x14\n\x07modelId\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x05 \x01(\tH\x03\x88\x01\x01\x12\x1b\n\x0epricePerTokens\x18\x06 \x01(\x02H\x04\x88\x01\x01\x12\x16\n\tmaxTokens\x18\x07 \x01(\x05H\x05\x88\x01\x01\x12\x18\n\x0btemperature\x18\x08 \x01(\x02H\x06\x88\x01\x01\x12\x19\n\x0cproviderType\x18\t \x01(\tH\x07\x88\x01\x01\x12\x15\n\x08isActive\x18\n \x01(\x08H\x08\x88\x01\x01\x12\x16\n\tisDefault\x18\x0b \x01(\x08H\t\x88\x01\x01\x42\r\n\x0b_providerIdB\x08\n\x06_modelB\n\n\x08_modelIdB\x0e\n\x0c_descriptionB\x11\n\x0f_pricePerTokensB\x0c\n\n_maxTokensB\x0e\n\x0c_temperatureB\x0f\n\r_providerTypeB\x0b\n\t_isActiveB\x0c\n\n_isDefault\"d\n\rModelResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\'\n\x05model\x18\x03 \x01(\x0b\x32\x13.provider.ModelInfoH\x00\x88\x01\x01\x42\x08\n\x06_model\"\x89\x01\n\x12ListModelsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12#\n\x06models\x18\x03 \x03(\x0b\x32\x13.provider.ModelInfo\x12,\n\npagination\x18\x04 \x01(\x0b\x32\x18.provider.PaginationInfo\"\x80\x03\n\tModelInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\nproviderId\x18\x02 \x01(\t\x12\r\n\x05model\x18\x03 \x01(\t\x12\x0f\n\x07modelId\x18\x04 \x01(\t\x12\x18\n\x0b\x64\x65scription\x18\x05 \x01(\tH\x00\x88\x01\x01\x12\x1b\n\x0epricePerTokens\x18\x06 \x01(\x02H\x01\x88\x01\x01\x12\x16\n\tmaxTokens\x18\x07 \x01(\x05H\x02\x88\x01\x01\x12\x18\n\x0btemperature\x18\x08 \x01(\x02H\x03\x88\x01\x01\x12\x14\n\x0cproviderType\x18\t \x01(\t\x12\x10\n\x08isActive\x18\n \x01(\x08\x12\x11\n\tisDefault\x18\x0b \x01(\x08\x12\x11\n\tcreatedAt\x18\x0c \x01(\t\x12\x11\n\tupdatedAt\x18\r \x01(\t\x12(\n\x08provider\x18\x0e \x01(\x0b\x32\x16.provider.ProviderInfoB\x0e\n\x0c_descriptionB\x11\n\x0f_pricePerTokensB\x0c\n\n_maxTokensB\x0e\n\x0c_temperature\"_\n\x0ePaginationInfo\x12\x13\n\x0b\x63urrentPage\x18\x01 \x01(\x05\x12\x12\n\ntotalPages\x18\x02 \x01(\x05\x12\x12\n\ntotalItems\x18\x03 \x01(\x05\x12\x10\n\x08pageSize\x18\x04 \x01(\x05\x32\x82\x03\n\x0fProviderService\x12M\n\x0e\x43reateProvider\x12\x1f.provider.CreateProviderRequest\x1a\x1a.provider.ProviderResponse\x12\x43\n\x0bGetProvider\x12\x18.provider.GetByIdRequest\x1a\x1a.provider.ProviderResponse\x12M\n\x0eUpdateProvider\x12\x1f.provider.UpdateProviderRequest\x1a\x1a.provider.ProviderResponse\x12\x43\n\x0e\x44\x65leteProvider\x12\x17.provider.DeleteRequest\x1a\x18.provider.DeleteResponse\x12G\n\rListProviders\x12\x15.provider.ListRequest\x1a\x1f.provider.ListProvidersResponse2\xb4\x03\n\x0cModelService\x12\x44\n\x0b\x43reateModel\x12\x1c.provider.CreateModelRequest\x1a\x17.provider.ModelResponse\x12=\n\x08GetModel\x12\x18.provider.GetByIdRequest\x1a\x17.provider.ModelResponse\x12\x44\n\x0bUpdateModel\x12\x1c.provider.UpdateModelRequest\x1a\x17.provider.ModelResponse\x12@\n\x0b\x44\x65leteModel\x12\x17.provider.DeleteRequest\x1a\x18.provider.DeleteResponse\x12\x41\n\nListModels\x12\x15.provider.ListRequest\x1a\x1c.provider.ListModelsResponse\x12T\n\x14ListModelsByProvider\x12\x1e.provider.GetByProviderRequest\x1a\x1c.provider.ListModelsResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'provider_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_GETBYIDREQUEST']._serialized_start=28
  _globals['_GETBYIDREQUEST']._serialized_end=56
  _globals['_DELETEREQUEST']._serialized_start=58
  _globals['_DELETEREQUEST']._serialized_end=85
  _globals['_DELETERESPONSE']._serialized_start=87
  _globals['_DELETERESPONSE']._serialized_end=137
  _globals['_LISTREQUEST']._serialized_start=139
  _globals['_LISTREQUEST']._serialized_end=260
  _globals['_GETBYPROVIDERREQUEST']._serialized_start=262
  _globals['_GETBYPROVIDERREQUEST']._serialized_end=372
  _globals['_CREATEPROVIDERREQUEST']._serialized_start=375
  _globals['_CREATEPROVIDERREQUEST']._serialized_end=549
  _globals['_UPDATEPROVIDERREQUEST']._serialized_start=552
  _globals['_UPDATEPROVIDERREQUEST']._serialized_end=773
  _globals['_PROVIDERRESPONSE']._serialized_start=775
  _globals['_PROVIDERRESPONSE']._serialized_end=887
  _globals['_LISTPROVIDERSRESPONSE']._serialized_start=890
  _globals['_LISTPROVIDERSRESPONSE']._serialized_end=1036
  _globals['_PROVIDERINFO']._serialized_start=1039
  _globals['_PROVIDERINFO']._serialized_end=1237
  _globals['_CREATEMODELREQUEST']._serialized_start=1240
  _globals['_CREATEMODELREQUEST']._serialized_end=1600
  _globals['_UPDATEMODELREQUEST']._serialized_start=1603
  _globals['_UPDATEMODELREQUEST']._serialized_end=2027
  _globals['_MODELRESPONSE']._serialized_start=2029
  _globals['_MODELRESPONSE']._serialized_end=2129
  _globals['_LISTMODELSRESPONSE']._serialized_start=2132
  _globals['_LISTMODELSRESPONSE']._serialized_end=2269
  _globals['_MODELINFO']._serialized_start=2272
  _globals['_MODELINFO']._serialized_end=2656
  _globals['_PAGINATIONINFO']._serialized_start=2658
  _globals['_PAGINATIONINFO']._serialized_end=2753
  _globals['_PROVIDERSERVICE']._serialized_start=2756
  _globals['_PROVIDERSERVICE']._serialized_end=3142
  _globals['_MODELSERVICE']._serialized_start=3145
  _globals['_MODELSERVICE']._serialized_end=3581
# @@protoc_insertion_point(module_scope)
