# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: payment.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'payment.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rpayment.proto\x12\x07payment\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"9\n\x0fGenericResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\"\xc9\x01\n\x18OrganisationEntitlements\x12\x0e\n\x06org_id\x18\x01 \x01(\t\x12$\n\tplan_type\x18\x02 \x01(\x0e\x32\x11.payment.PlanType\x12\x1e\n\x16\x63urrent_credit_balance\x18\x03 \x01(\x01\x12;\n\x17\x65ntitlements_updated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1a\n\x12stripe_customer_id\x18\x08 \x01(\t\";\n\x13\x41\x63tivatePlanRequest\x12\x0e\n\x06org_id\x18\x01 \x01(\t\x12\x14\n\x0cplan_id_code\x18\x02 \x01(\t\"\x98\x01\n\x14\x44\x65\x64uctCreditsRequest\x12\x0e\n\x06org_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x10\n\x08\x61gent_id\x18\x03 \x01(\t\x12\x19\n\x11\x63redits_to_deduct\x18\x04 \x01(\x01\x12\x32\n\x11reference_details\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\"d\n\x15\x44\x65\x64uctCreditsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12#\n\x1bnew_balance_after_deduction\x18\x03 \x01(\x01\"u\n#CreateProPlanCheckoutSessionRequest\x12\x0e\n\x06org_id\x18\x01 \x01(\t\x12\x15\n\ruser_id_admin\x18\x02 \x01(\t\x12\x13\n\x0bsuccess_url\x18\x03 \x01(\t\x12\x12\n\ncancel_url\x18\x04 \x01(\t\"r\n\x1eProPlanCheckoutSessionResponse\x12\x14\n\x0c\x63heckout_url\x18\x01 \x01(\t\x12\x12\n\nsession_id\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x15\n\rerror_message\x18\x04 \x01(\t\":\n\x14StripeWebhookRequest\x12\x0f\n\x07payload\x18\x01 \x01(\t\x12\x11\n\tsignature\x18\x02 \x01(\t\"\xc6\x02\n\x11\x43reditLedgerEntry\x12\x16\n\x0etransaction_id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x15\n\ruser_id_actor\x18\x03 \x01(\t\x12-\n\ttimestamp\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x38\n\x10transaction_type\x18\x05 \x01(\x0e\x32\x1e.payment.CreditTransactionType\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12!\n\x19\x62\x61lance_after_transaction\x18\x07 \x01(\x01\x12\x14\n\x0creference_id\x18\x08 \x01(\t\x12\r\n\x05notes\x18\t \x01(\t\x12(\n\x07\x64\x65tails\x18\n \x01(\x0b\x32\x17.google.protobuf.Struct\"\xee\x01\n\x16GetCreditLedgerRequest\x12\x0e\n\x06org_id\x18\x01 \x01(\t\x12.\n\nstart_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12?\n\x17transaction_type_filter\x18\x04 \x01(\x0e\x32\x1e.payment.CreditTransactionType\x12\x11\n\tpage_size\x18\x05 \x01(\x05\x12\x12\n\npage_token\x18\x06 \x01(\t\"\x87\x01\n\x17GetCreditLedgerResponse\x12+\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\x1a.payment.CreditLedgerEntry\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x15\n\rerror_message\x18\x04 \x01(\t\"\xb5\x01\n!AdminCreateCheckoutSessionRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0e\n\x06org_id\x18\x02 \x01(\t\x12\"\n\x04mode\x18\x03 \x01(\x0e\x32\x14.payment.PaymentMode\x12\x10\n\x08price_id\x18\x04 \x01(\t\x12\x13\n\x0bsuccess_url\x18\x05 \x01(\t\x12\x12\n\ncancel_url\x18\x06 \x01(\t\x12\x10\n\x08quantity\x18\x07 \x01(\x05\"u\n\"AdminCreateCheckoutSessionResponse\x12\x13\n\x0bsession_url\x18\x01 \x01(\t\x12\x12\n\nsession_id\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x15\n\rerror_message\x18\x04 \x01(\t\"_\n\"CreateCustomerPortalSessionRequest\x12\x15\n\ruser_id_admin\x18\x01 \x01(\t\x12\x0e\n\x06org_id\x18\x02 \x01(\t\x12\x12\n\nreturn_url\x18\x03 \x01(\t\"a\n#CreateCustomerPortalSessionResponse\x12\x12\n\nportal_url\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\"\xbc\x03\n\x10SubscriptionInfo\x12\x17\n\x0fsubscription_id\x18\x01 \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x02 \x01(\t\x12$\n\tplan_type\x18\x03 \x01(\x0e\x32\x11.payment.PlanType\x12&\n\x06status\x18\x04 \x01(\x0e\x32\x16.payment.PaymentStatus\x12\x38\n\x14\x63urrent_period_start\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x63urrent_period_end\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1c\n\x14\x63\x61ncel_at_period_end\x18\t \x01(\x08\x12,\n$monthly_credits_associated_with_plan\x18\n \x01(\x01\x12\x0e\n\x06org_id\x18\x0b \x01(\t\"/\n\x1dGetSubscriptionDetailsRequest\x12\x0e\n\x06org_id\x18\x01 \x01(\t\"y\n\x1eGetSubscriptionDetailsResponse\x12/\n\x0csubscription\x18\x01 \x01(\x0b\x32\x19.payment.SubscriptionInfo\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\">\n\x19\x43\x61ncelSubscriptionRequest\x12\x0e\n\x06org_id\x18\x01 \x01(\t\x12\x11\n\timmediate\x18\x02 \x01(\x08\"\x85\x01\n\x1a\x43\x61ncelSubscriptionResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12?\n\x1b\x65\x66\x66\x65\x63tive_cancellation_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp*F\n\x08PlanType\x12\x19\n\x15PLAN_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04\x46REE\x10\x01\x12\x0c\n\x08STANDARD\x10\x02\x12\x07\n\x03PRO\x10\x03*J\n\x0bPaymentMode\x12\x1c\n\x18PAYMENT_MODE_UNSPECIFIED\x10\x00\x12\x10\n\x0cSUBSCRIPTION\x10\x01\x12\x0b\n\x07PAYMENT\x10\x02*\x8b\x01\n\rPaymentStatus\x12\x1e\n\x1aPAYMENT_STATUS_UNSPECIFIED\x10\x00\x12\x15\n\x11NO_PAYMENT_METHOD\x10\x01\x12\x17\n\x13\x41\x43TIVE_SUBSCRIPTION\x10\x02\x12\x0c\n\x08PAST_DUE\x10\x03\x12\x0c\n\x08\x43\x41NCELED\x10\x04\x12\x0e\n\nINCOMPLETE\x10\x05*d\n\x0bModelAccess\x12\x1c\n\x18MODEL_ACCESS_UNSPECIFIED\x10\x00\x12\x08\n\x04\x42\x41SE\x10\x01\x12\x19\n\x15STANDARD_MODEL_ACCESS\x10\x02\x12\x12\n\x0ePREMIUM_CUSTOM\x10\x03*W\n\x0cLogRetention\x12\x1d\n\x19LOG_RETENTION_UNSPECIFIED\x10\x00\x12\x08\n\x04NONE\x10\x01\x12\x10\n\x0cTHREE_MONTHS\x10\x02\x12\x0c\n\x08ONE_YEAR\x10\x03*Y\n\x0bSupportTier\x12\x1c\n\x18SUPPORT_TIER_UNSPECIFIED\x10\x00\x12\r\n\tCOMMUNITY\x10\x01\x12\t\n\x05\x45MAIL\x10\x02\x12\x12\n\x0e\x44\x45\x44ICATED_24_7\x10\x03*\xbf\x01\n\x15\x43reditTransactionType\x12\'\n#CREDIT_TRANSACTION_TYPE_UNSPECIFIED\x10\x00\x12\x16\n\x12INITIAL_ALLOCATION\x10\x01\x12\x13\n\x0fUSAGE_DEDUCTION\x10\x02\x12\x11\n\rPLAN_PURCHASE\x10\x03\x12\x1a\n\x16PLAN_CHANGE_ADJUSTMENT\x10\x04\x12\x15\n\x11MANUAL_ADJUSTMENT\x10\x05\x12\n\n\x06REFUND\x10\x06\x32\x87\x07\n\x0ePaymentService\x12M\n\x13\x41\x63tivateDefaultPlan\x12\x1c.payment.ActivatePlanRequest\x1a\x18.payment.GenericResponse\x12N\n\rDeductCredits\x12\x1d.payment.DeductCreditsRequest\x1a\x1e.payment.DeductCreditsResponse\x12u\n\x1c\x43reateProPlanCheckoutSession\x12,.payment.CreateProPlanCheckoutSessionRequest\x1a\'.payment.ProPlanCheckoutSessionResponse\x12N\n\x13HandleStripeWebhook\x12\x1d.payment.StripeWebhookRequest\x1a\x18.payment.GenericResponse\x12T\n\x0fGetCreditLedger\x12\x1f.payment.GetCreditLedgerRequest\x1a .payment.GetCreditLedgerResponse\x12u\n\x1a\x41\x64minCreateCheckoutSession\x12*.payment.AdminCreateCheckoutSessionRequest\x1a+.payment.AdminCreateCheckoutSessionResponse\x12x\n\x1b\x43reateCustomerPortalSession\x12+.payment.CreateCustomerPortalSessionRequest\x1a,.payment.CreateCustomerPortalSessionResponse\x12i\n\x16GetSubscriptionDetails\x12&.payment.GetSubscriptionDetailsRequest\x1a\'.payment.GetSubscriptionDetailsResponse\x12]\n\x12\x43\x61ncelSubscription\x12\".payment.CancelSubscriptionRequest\x1a#.payment.CancelSubscriptionResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'payment_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PLANTYPE']._serialized_start=2991
  _globals['_PLANTYPE']._serialized_end=3061
  _globals['_PAYMENTMODE']._serialized_start=3063
  _globals['_PAYMENTMODE']._serialized_end=3137
  _globals['_PAYMENTSTATUS']._serialized_start=3140
  _globals['_PAYMENTSTATUS']._serialized_end=3279
  _globals['_MODELACCESS']._serialized_start=3281
  _globals['_MODELACCESS']._serialized_end=3381
  _globals['_LOGRETENTION']._serialized_start=3383
  _globals['_LOGRETENTION']._serialized_end=3470
  _globals['_SUPPORTTIER']._serialized_start=3472
  _globals['_SUPPORTTIER']._serialized_end=3561
  _globals['_CREDITTRANSACTIONTYPE']._serialized_start=3564
  _globals['_CREDITTRANSACTIONTYPE']._serialized_end=3755
  _globals['_GENERICRESPONSE']._serialized_start=89
  _globals['_GENERICRESPONSE']._serialized_end=146
  _globals['_ORGANISATIONENTITLEMENTS']._serialized_start=149
  _globals['_ORGANISATIONENTITLEMENTS']._serialized_end=350
  _globals['_ACTIVATEPLANREQUEST']._serialized_start=352
  _globals['_ACTIVATEPLANREQUEST']._serialized_end=411
  _globals['_DEDUCTCREDITSREQUEST']._serialized_start=414
  _globals['_DEDUCTCREDITSREQUEST']._serialized_end=566
  _globals['_DEDUCTCREDITSRESPONSE']._serialized_start=568
  _globals['_DEDUCTCREDITSRESPONSE']._serialized_end=668
  _globals['_CREATEPROPLANCHECKOUTSESSIONREQUEST']._serialized_start=670
  _globals['_CREATEPROPLANCHECKOUTSESSIONREQUEST']._serialized_end=787
  _globals['_PROPLANCHECKOUTSESSIONRESPONSE']._serialized_start=789
  _globals['_PROPLANCHECKOUTSESSIONRESPONSE']._serialized_end=903
  _globals['_STRIPEWEBHOOKREQUEST']._serialized_start=905
  _globals['_STRIPEWEBHOOKREQUEST']._serialized_end=963
  _globals['_CREDITLEDGERENTRY']._serialized_start=966
  _globals['_CREDITLEDGERENTRY']._serialized_end=1292
  _globals['_GETCREDITLEDGERREQUEST']._serialized_start=1295
  _globals['_GETCREDITLEDGERREQUEST']._serialized_end=1533
  _globals['_GETCREDITLEDGERRESPONSE']._serialized_start=1536
  _globals['_GETCREDITLEDGERRESPONSE']._serialized_end=1671
  _globals['_ADMINCREATECHECKOUTSESSIONREQUEST']._serialized_start=1674
  _globals['_ADMINCREATECHECKOUTSESSIONREQUEST']._serialized_end=1855
  _globals['_ADMINCREATECHECKOUTSESSIONRESPONSE']._serialized_start=1857
  _globals['_ADMINCREATECHECKOUTSESSIONRESPONSE']._serialized_end=1974
  _globals['_CREATECUSTOMERPORTALSESSIONREQUEST']._serialized_start=1976
  _globals['_CREATECUSTOMERPORTALSESSIONREQUEST']._serialized_end=2071
  _globals['_CREATECUSTOMERPORTALSESSIONRESPONSE']._serialized_start=2073
  _globals['_CREATECUSTOMERPORTALSESSIONRESPONSE']._serialized_end=2170
  _globals['_SUBSCRIPTIONINFO']._serialized_start=2173
  _globals['_SUBSCRIPTIONINFO']._serialized_end=2617
  _globals['_GETSUBSCRIPTIONDETAILSREQUEST']._serialized_start=2619
  _globals['_GETSUBSCRIPTIONDETAILSREQUEST']._serialized_end=2666
  _globals['_GETSUBSCRIPTIONDETAILSRESPONSE']._serialized_start=2668
  _globals['_GETSUBSCRIPTIONDETAILSRESPONSE']._serialized_end=2789
  _globals['_CANCELSUBSCRIPTIONREQUEST']._serialized_start=2791
  _globals['_CANCELSUBSCRIPTIONREQUEST']._serialized_end=2853
  _globals['_CANCELSUBSCRIPTIONRESPONSE']._serialized_start=2856
  _globals['_CANCELSUBSCRIPTIONRESPONSE']._serialized_end=2989
  _globals['_PAYMENTSERVICE']._serialized_start=3758
  _globals['_PAYMENTSERVICE']._serialized_end=4661
# @@protoc_insertion_point(module_scope)
