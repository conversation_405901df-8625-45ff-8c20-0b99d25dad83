# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import organisation_pb2 as organisation__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in organisation_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class OrganisationServiceStub(object):
    """The Organisation service definition
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.createOrganisation = channel.unary_unary(
                '/organisation.OrganisationService/createOrganisation',
                request_serializer=organisation__pb2.CreateOrganisationRequest.SerializeToString,
                response_deserializer=organisation__pb2.OrganisationResponse.FromString,
                _registered_method=True)
        self.getOrganisation = channel.unary_unary(
                '/organisation.OrganisationService/getOrganisation',
                request_serializer=organisation__pb2.GetOrganisationRequest.SerializeToString,
                response_deserializer=organisation__pb2.OrganisationResponse.FromString,
                _registered_method=True)
        self.updateOrganisation = channel.unary_unary(
                '/organisation.OrganisationService/updateOrganisation',
                request_serializer=organisation__pb2.UpdateOrganisationRequest.SerializeToString,
                response_deserializer=organisation__pb2.OrganisationResponse.FromString,
                _registered_method=True)
        self.createDepartment = channel.unary_unary(
                '/organisation.OrganisationService/createDepartment',
                request_serializer=organisation__pb2.CreateDepartmentRequest.SerializeToString,
                response_deserializer=organisation__pb2.DepartmentResponse.FromString,
                _registered_method=True)
        self.listDepartments = channel.unary_unary(
                '/organisation.OrganisationService/listDepartments',
                request_serializer=organisation__pb2.ListDepartmentsRequest.SerializeToString,
                response_deserializer=organisation__pb2.ListDepartmentsResponse.FromString,
                _registered_method=True)
        self.inviteUser = channel.unary_unary(
                '/organisation.OrganisationService/inviteUser',
                request_serializer=organisation__pb2.InviteUserRequest.SerializeToString,
                response_deserializer=organisation__pb2.InviteResponse.FromString,
                _registered_method=True)
        self.acceptInviteByLink = channel.unary_unary(
                '/organisation.OrganisationService/acceptInviteByLink',
                request_serializer=organisation__pb2.AcceptInviteByLinkRequest.SerializeToString,
                response_deserializer=organisation__pb2.InviteResponse.FromString,
                _registered_method=True)
        self.getUserOrganisations = channel.unary_unary(
                '/organisation.OrganisationService/getUserOrganisations',
                request_serializer=organisation__pb2.GetUserOrganisationsRequest.SerializeToString,
                response_deserializer=organisation__pb2.UserOrganisationsResponse.FromString,
                _registered_method=True)
        self.grantDepartmentAccess = channel.unary_unary(
                '/organisation.OrganisationService/grantDepartmentAccess',
                request_serializer=organisation__pb2.GrantDepartmentAccessRequest.SerializeToString,
                response_deserializer=organisation__pb2.GrantDepartmentAccessResponse.FromString,
                _registered_method=True)
        self.batchGrantDepartmentAccess = channel.unary_unary(
                '/organisation.OrganisationService/batchGrantDepartmentAccess',
                request_serializer=organisation__pb2.BatchGrantDepartmentAccessRequest.SerializeToString,
                response_deserializer=organisation__pb2.BatchGrantDepartmentAccessResponse.FromString,
                _registered_method=True)
        self.listTopLevelFolders = channel.unary_unary(
                '/organisation.OrganisationService/listTopLevelFolders',
                request_serializer=organisation__pb2.ListTopLevelFoldersRequest.SerializeToString,
                response_deserializer=organisation__pb2.ListTopLevelFoldersResponse.FromString,
                _registered_method=True)
        self.addSource = channel.unary_unary(
                '/organisation.OrganisationService/addSource',
                request_serializer=organisation__pb2.AddSourceRequest.SerializeToString,
                response_deserializer=organisation__pb2.AddSourceResponse.FromString,
                _registered_method=True)
        self.listSources = channel.unary_unary(
                '/organisation.OrganisationService/listSources',
                request_serializer=organisation__pb2.ListSourcesRequest.SerializeToString,
                response_deserializer=organisation__pb2.ListSourcesResponse.FromString,
                _registered_method=True)
        self.deleteSource = channel.unary_unary(
                '/organisation.OrganisationService/deleteSource',
                request_serializer=organisation__pb2.DeleteSourceRequest.SerializeToString,
                response_deserializer=organisation__pb2.DeleteSourceResponse.FromString,
                _registered_method=True)
        self.updateSourceCredentials = channel.unary_unary(
                '/organisation.OrganisationService/updateSourceCredentials',
                request_serializer=organisation__pb2.UpdateSourceCredentialsRequest.SerializeToString,
                response_deserializer=organisation__pb2.UpdateSourceCredentialsResponse.FromString,
                _registered_method=True)
        self.validateSource = channel.unary_unary(
                '/organisation.OrganisationService/validateSource',
                request_serializer=organisation__pb2.ValidateSourceRequest.SerializeToString,
                response_deserializer=organisation__pb2.ValidateSourceResponse.FromString,
                _registered_method=True)
        self.getInviterInvites = channel.unary_unary(
                '/organisation.OrganisationService/getInviterInvites',
                request_serializer=organisation__pb2.ListInviterInvitesRequest.SerializeToString,
                response_deserializer=organisation__pb2.ListInviterInvitesResponse.FromString,
                _registered_method=True)
        self.getDepartmentUsers = channel.unary_unary(
                '/organisation.OrganisationService/getDepartmentUsers',
                request_serializer=organisation__pb2.GetDepartmentUsersRequest.SerializeToString,
                response_deserializer=organisation__pb2.GetDepartmentUsersResponse.FromString,
                _registered_method=True)
        self.listDepartmentFolders = channel.unary_unary(
                '/organisation.OrganisationService/listDepartmentFolders',
                request_serializer=organisation__pb2.ListDepartmentFoldersRequest.SerializeToString,
                response_deserializer=organisation__pb2.ListDepartmentFoldersResponse.FromString,
                _registered_method=True)
        self.addMemberToDepartment = channel.unary_unary(
                '/organisation.OrganisationService/addMemberToDepartment',
                request_serializer=organisation__pb2.AddMemberToDepartmentRequest.SerializeToString,
                response_deserializer=organisation__pb2.AddMemberToDepartmentResponse.FromString,
                _registered_method=True)


class OrganisationServiceServicer(object):
    """The Organisation service definition
    """

    def createOrganisation(self, request, context):
        """Create a new organisation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getOrganisation(self, request, context):
        """Get an organisation by ID
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateOrganisation(self, request, context):
        """Update an existing organisation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createDepartment(self, request, context):
        """// List organisations (with optional filters)
        rpc ListOrganisations (ListOrganisationsRequest) returns (ListOrganisationsResponse) {}

        Create a new department
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listDepartments(self, request, context):
        """// Get a department by ID
        rpc GetDepartment (GetDepartmentRequest) returns (DepartmentResponse) {}

        List departments in an organisation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def inviteUser(self, request, context):
        """Create an invitation for a user to join an organisation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def acceptInviteByLink(self, request, context):
        """// Get an invite by ID
        rpc GetInvite (GetInviteRequest) returns (InviteResponse) {}

        // List invites for an organisation
        rpc ListInvites (ListInvitesRequest) returns (ListInvitesResponse) {}

        Accept an invite using an invite link
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getUserOrganisations(self, request, context):
        """Get a user's organisations (primary and all)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def grantDepartmentAccess(self, request, context):
        """Grant a department access to files and folders
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def batchGrantDepartmentAccess(self, request, context):
        """Grant multiple departments access to files and folders in a batch operation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listTopLevelFolders(self, request, context):
        """List top-level folders accessible by a user
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def addSource(self, request, context):
        """Add a new source with credentials
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listSources(self, request, context):
        """List sources for an organisation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteSource(self, request, context):
        """Delete a source
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateSourceCredentials(self, request, context):
        """Update source credentials
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def validateSource(self, request, context):
        """Validate source and get accessible folders
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getInviterInvites(self, request, context):
        """List invites sent by a user (inviter)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getDepartmentUsers(self, request, context):
        """Get all users in a department
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listDepartmentFolders(self, request, context):
        """List folders accessible by specified departments
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def addMemberToDepartment(self, request, context):
        """Add a member to a department
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OrganisationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'createOrganisation': grpc.unary_unary_rpc_method_handler(
                    servicer.createOrganisation,
                    request_deserializer=organisation__pb2.CreateOrganisationRequest.FromString,
                    response_serializer=organisation__pb2.OrganisationResponse.SerializeToString,
            ),
            'getOrganisation': grpc.unary_unary_rpc_method_handler(
                    servicer.getOrganisation,
                    request_deserializer=organisation__pb2.GetOrganisationRequest.FromString,
                    response_serializer=organisation__pb2.OrganisationResponse.SerializeToString,
            ),
            'updateOrganisation': grpc.unary_unary_rpc_method_handler(
                    servicer.updateOrganisation,
                    request_deserializer=organisation__pb2.UpdateOrganisationRequest.FromString,
                    response_serializer=organisation__pb2.OrganisationResponse.SerializeToString,
            ),
            'createDepartment': grpc.unary_unary_rpc_method_handler(
                    servicer.createDepartment,
                    request_deserializer=organisation__pb2.CreateDepartmentRequest.FromString,
                    response_serializer=organisation__pb2.DepartmentResponse.SerializeToString,
            ),
            'listDepartments': grpc.unary_unary_rpc_method_handler(
                    servicer.listDepartments,
                    request_deserializer=organisation__pb2.ListDepartmentsRequest.FromString,
                    response_serializer=organisation__pb2.ListDepartmentsResponse.SerializeToString,
            ),
            'inviteUser': grpc.unary_unary_rpc_method_handler(
                    servicer.inviteUser,
                    request_deserializer=organisation__pb2.InviteUserRequest.FromString,
                    response_serializer=organisation__pb2.InviteResponse.SerializeToString,
            ),
            'acceptInviteByLink': grpc.unary_unary_rpc_method_handler(
                    servicer.acceptInviteByLink,
                    request_deserializer=organisation__pb2.AcceptInviteByLinkRequest.FromString,
                    response_serializer=organisation__pb2.InviteResponse.SerializeToString,
            ),
            'getUserOrganisations': grpc.unary_unary_rpc_method_handler(
                    servicer.getUserOrganisations,
                    request_deserializer=organisation__pb2.GetUserOrganisationsRequest.FromString,
                    response_serializer=organisation__pb2.UserOrganisationsResponse.SerializeToString,
            ),
            'grantDepartmentAccess': grpc.unary_unary_rpc_method_handler(
                    servicer.grantDepartmentAccess,
                    request_deserializer=organisation__pb2.GrantDepartmentAccessRequest.FromString,
                    response_serializer=organisation__pb2.GrantDepartmentAccessResponse.SerializeToString,
            ),
            'batchGrantDepartmentAccess': grpc.unary_unary_rpc_method_handler(
                    servicer.batchGrantDepartmentAccess,
                    request_deserializer=organisation__pb2.BatchGrantDepartmentAccessRequest.FromString,
                    response_serializer=organisation__pb2.BatchGrantDepartmentAccessResponse.SerializeToString,
            ),
            'listTopLevelFolders': grpc.unary_unary_rpc_method_handler(
                    servicer.listTopLevelFolders,
                    request_deserializer=organisation__pb2.ListTopLevelFoldersRequest.FromString,
                    response_serializer=organisation__pb2.ListTopLevelFoldersResponse.SerializeToString,
            ),
            'addSource': grpc.unary_unary_rpc_method_handler(
                    servicer.addSource,
                    request_deserializer=organisation__pb2.AddSourceRequest.FromString,
                    response_serializer=organisation__pb2.AddSourceResponse.SerializeToString,
            ),
            'listSources': grpc.unary_unary_rpc_method_handler(
                    servicer.listSources,
                    request_deserializer=organisation__pb2.ListSourcesRequest.FromString,
                    response_serializer=organisation__pb2.ListSourcesResponse.SerializeToString,
            ),
            'deleteSource': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteSource,
                    request_deserializer=organisation__pb2.DeleteSourceRequest.FromString,
                    response_serializer=organisation__pb2.DeleteSourceResponse.SerializeToString,
            ),
            'updateSourceCredentials': grpc.unary_unary_rpc_method_handler(
                    servicer.updateSourceCredentials,
                    request_deserializer=organisation__pb2.UpdateSourceCredentialsRequest.FromString,
                    response_serializer=organisation__pb2.UpdateSourceCredentialsResponse.SerializeToString,
            ),
            'validateSource': grpc.unary_unary_rpc_method_handler(
                    servicer.validateSource,
                    request_deserializer=organisation__pb2.ValidateSourceRequest.FromString,
                    response_serializer=organisation__pb2.ValidateSourceResponse.SerializeToString,
            ),
            'getInviterInvites': grpc.unary_unary_rpc_method_handler(
                    servicer.getInviterInvites,
                    request_deserializer=organisation__pb2.ListInviterInvitesRequest.FromString,
                    response_serializer=organisation__pb2.ListInviterInvitesResponse.SerializeToString,
            ),
            'getDepartmentUsers': grpc.unary_unary_rpc_method_handler(
                    servicer.getDepartmentUsers,
                    request_deserializer=organisation__pb2.GetDepartmentUsersRequest.FromString,
                    response_serializer=organisation__pb2.GetDepartmentUsersResponse.SerializeToString,
            ),
            'listDepartmentFolders': grpc.unary_unary_rpc_method_handler(
                    servicer.listDepartmentFolders,
                    request_deserializer=organisation__pb2.ListDepartmentFoldersRequest.FromString,
                    response_serializer=organisation__pb2.ListDepartmentFoldersResponse.SerializeToString,
            ),
            'addMemberToDepartment': grpc.unary_unary_rpc_method_handler(
                    servicer.addMemberToDepartment,
                    request_deserializer=organisation__pb2.AddMemberToDepartmentRequest.FromString,
                    response_serializer=organisation__pb2.AddMemberToDepartmentResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'organisation.OrganisationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('organisation.OrganisationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OrganisationService(object):
    """The Organisation service definition
    """

    @staticmethod
    def createOrganisation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/createOrganisation',
            organisation__pb2.CreateOrganisationRequest.SerializeToString,
            organisation__pb2.OrganisationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getOrganisation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/getOrganisation',
            organisation__pb2.GetOrganisationRequest.SerializeToString,
            organisation__pb2.OrganisationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateOrganisation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/updateOrganisation',
            organisation__pb2.UpdateOrganisationRequest.SerializeToString,
            organisation__pb2.OrganisationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createDepartment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/createDepartment',
            organisation__pb2.CreateDepartmentRequest.SerializeToString,
            organisation__pb2.DepartmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listDepartments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/listDepartments',
            organisation__pb2.ListDepartmentsRequest.SerializeToString,
            organisation__pb2.ListDepartmentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def inviteUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/inviteUser',
            organisation__pb2.InviteUserRequest.SerializeToString,
            organisation__pb2.InviteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def acceptInviteByLink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/acceptInviteByLink',
            organisation__pb2.AcceptInviteByLinkRequest.SerializeToString,
            organisation__pb2.InviteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getUserOrganisations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/getUserOrganisations',
            organisation__pb2.GetUserOrganisationsRequest.SerializeToString,
            organisation__pb2.UserOrganisationsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def grantDepartmentAccess(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/grantDepartmentAccess',
            organisation__pb2.GrantDepartmentAccessRequest.SerializeToString,
            organisation__pb2.GrantDepartmentAccessResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def batchGrantDepartmentAccess(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/batchGrantDepartmentAccess',
            organisation__pb2.BatchGrantDepartmentAccessRequest.SerializeToString,
            organisation__pb2.BatchGrantDepartmentAccessResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listTopLevelFolders(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/listTopLevelFolders',
            organisation__pb2.ListTopLevelFoldersRequest.SerializeToString,
            organisation__pb2.ListTopLevelFoldersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def addSource(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/addSource',
            organisation__pb2.AddSourceRequest.SerializeToString,
            organisation__pb2.AddSourceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listSources(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/listSources',
            organisation__pb2.ListSourcesRequest.SerializeToString,
            organisation__pb2.ListSourcesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteSource(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/deleteSource',
            organisation__pb2.DeleteSourceRequest.SerializeToString,
            organisation__pb2.DeleteSourceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateSourceCredentials(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/updateSourceCredentials',
            organisation__pb2.UpdateSourceCredentialsRequest.SerializeToString,
            organisation__pb2.UpdateSourceCredentialsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def validateSource(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/validateSource',
            organisation__pb2.ValidateSourceRequest.SerializeToString,
            organisation__pb2.ValidateSourceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getInviterInvites(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/getInviterInvites',
            organisation__pb2.ListInviterInvitesRequest.SerializeToString,
            organisation__pb2.ListInviterInvitesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getDepartmentUsers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/getDepartmentUsers',
            organisation__pb2.GetDepartmentUsersRequest.SerializeToString,
            organisation__pb2.GetDepartmentUsersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listDepartmentFolders(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/listDepartmentFolders',
            organisation__pb2.ListDepartmentFoldersRequest.SerializeToString,
            organisation__pb2.ListDepartmentFoldersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def addMemberToDepartment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/organisation.OrganisationService/addMemberToDepartment',
            organisation__pb2.AddMemberToDepartmentRequest.SerializeToString,
            organisation__pb2.AddMemberToDepartmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
