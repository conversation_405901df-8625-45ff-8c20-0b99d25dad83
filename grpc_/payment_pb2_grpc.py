# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import payment_pb2 as payment__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in payment_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class PaymentServiceStub(object):
    """Payment Service Definition
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ActivateDefaultPlan = channel.unary_unary(
                '/payment.PaymentService/ActivateDefaultPlan',
                request_serializer=payment__pb2.ActivatePlanRequest.SerializeToString,
                response_deserializer=payment__pb2.GenericResponse.FromString,
                _registered_method=True)
        self.DeductCredits = channel.unary_unary(
                '/payment.PaymentService/DeductCredits',
                request_serializer=payment__pb2.DeductCreditsRequest.SerializeToString,
                response_deserializer=payment__pb2.DeductCreditsResponse.FromString,
                _registered_method=True)
        self.CreateProPlanCheckoutSession = channel.unary_unary(
                '/payment.PaymentService/CreateProPlanCheckoutSession',
                request_serializer=payment__pb2.CreateProPlanCheckoutSessionRequest.SerializeToString,
                response_deserializer=payment__pb2.ProPlanCheckoutSessionResponse.FromString,
                _registered_method=True)
        self.HandleStripeWebhook = channel.unary_unary(
                '/payment.PaymentService/HandleStripeWebhook',
                request_serializer=payment__pb2.StripeWebhookRequest.SerializeToString,
                response_deserializer=payment__pb2.GenericResponse.FromString,
                _registered_method=True)
        self.GetCreditLedger = channel.unary_unary(
                '/payment.PaymentService/GetCreditLedger',
                request_serializer=payment__pb2.GetCreditLedgerRequest.SerializeToString,
                response_deserializer=payment__pb2.GetCreditLedgerResponse.FromString,
                _registered_method=True)
        self.AdminCreateCheckoutSession = channel.unary_unary(
                '/payment.PaymentService/AdminCreateCheckoutSession',
                request_serializer=payment__pb2.AdminCreateCheckoutSessionRequest.SerializeToString,
                response_deserializer=payment__pb2.AdminCreateCheckoutSessionResponse.FromString,
                _registered_method=True)
        self.CreateCustomerPortalSession = channel.unary_unary(
                '/payment.PaymentService/CreateCustomerPortalSession',
                request_serializer=payment__pb2.CreateCustomerPortalSessionRequest.SerializeToString,
                response_deserializer=payment__pb2.CreateCustomerPortalSessionResponse.FromString,
                _registered_method=True)
        self.GetSubscriptionDetails = channel.unary_unary(
                '/payment.PaymentService/GetSubscriptionDetails',
                request_serializer=payment__pb2.GetSubscriptionDetailsRequest.SerializeToString,
                response_deserializer=payment__pb2.GetSubscriptionDetailsResponse.FromString,
                _registered_method=True)
        self.CancelSubscription = channel.unary_unary(
                '/payment.PaymentService/CancelSubscription',
                request_serializer=payment__pb2.CancelSubscriptionRequest.SerializeToString,
                response_deserializer=payment__pb2.CancelSubscriptionResponse.FromString,
                _registered_method=True)


class PaymentServiceServicer(object):
    """Payment Service Definition
    """

    def ActivateDefaultPlan(self, request, context):
        """Core RPCs based on action.md
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeductCredits(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateProPlanCheckoutSession(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HandleStripeWebhook(self, request, context):
        """Handles various Stripe events
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCreditLedger(self, request, context):
        """RPC for fetching credit ledger (for admin/display purposes)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AdminCreateCheckoutSession(self, request, context):
        """RPCs adapted from original payment.proto, potentially for admin use or direct Stripe management
        Renamed
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateCustomerPortalSession(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSubscriptionDetails(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelSubscription(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PaymentServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ActivateDefaultPlan': grpc.unary_unary_rpc_method_handler(
                    servicer.ActivateDefaultPlan,
                    request_deserializer=payment__pb2.ActivatePlanRequest.FromString,
                    response_serializer=payment__pb2.GenericResponse.SerializeToString,
            ),
            'DeductCredits': grpc.unary_unary_rpc_method_handler(
                    servicer.DeductCredits,
                    request_deserializer=payment__pb2.DeductCreditsRequest.FromString,
                    response_serializer=payment__pb2.DeductCreditsResponse.SerializeToString,
            ),
            'CreateProPlanCheckoutSession': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateProPlanCheckoutSession,
                    request_deserializer=payment__pb2.CreateProPlanCheckoutSessionRequest.FromString,
                    response_serializer=payment__pb2.ProPlanCheckoutSessionResponse.SerializeToString,
            ),
            'HandleStripeWebhook': grpc.unary_unary_rpc_method_handler(
                    servicer.HandleStripeWebhook,
                    request_deserializer=payment__pb2.StripeWebhookRequest.FromString,
                    response_serializer=payment__pb2.GenericResponse.SerializeToString,
            ),
            'GetCreditLedger': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCreditLedger,
                    request_deserializer=payment__pb2.GetCreditLedgerRequest.FromString,
                    response_serializer=payment__pb2.GetCreditLedgerResponse.SerializeToString,
            ),
            'AdminCreateCheckoutSession': grpc.unary_unary_rpc_method_handler(
                    servicer.AdminCreateCheckoutSession,
                    request_deserializer=payment__pb2.AdminCreateCheckoutSessionRequest.FromString,
                    response_serializer=payment__pb2.AdminCreateCheckoutSessionResponse.SerializeToString,
            ),
            'CreateCustomerPortalSession': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCustomerPortalSession,
                    request_deserializer=payment__pb2.CreateCustomerPortalSessionRequest.FromString,
                    response_serializer=payment__pb2.CreateCustomerPortalSessionResponse.SerializeToString,
            ),
            'GetSubscriptionDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSubscriptionDetails,
                    request_deserializer=payment__pb2.GetSubscriptionDetailsRequest.FromString,
                    response_serializer=payment__pb2.GetSubscriptionDetailsResponse.SerializeToString,
            ),
            'CancelSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelSubscription,
                    request_deserializer=payment__pb2.CancelSubscriptionRequest.FromString,
                    response_serializer=payment__pb2.CancelSubscriptionResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'payment.PaymentService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('payment.PaymentService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PaymentService(object):
    """Payment Service Definition
    """

    @staticmethod
    def ActivateDefaultPlan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/ActivateDefaultPlan',
            payment__pb2.ActivatePlanRequest.SerializeToString,
            payment__pb2.GenericResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeductCredits(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/DeductCredits',
            payment__pb2.DeductCreditsRequest.SerializeToString,
            payment__pb2.DeductCreditsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateProPlanCheckoutSession(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/CreateProPlanCheckoutSession',
            payment__pb2.CreateProPlanCheckoutSessionRequest.SerializeToString,
            payment__pb2.ProPlanCheckoutSessionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HandleStripeWebhook(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/HandleStripeWebhook',
            payment__pb2.StripeWebhookRequest.SerializeToString,
            payment__pb2.GenericResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCreditLedger(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/GetCreditLedger',
            payment__pb2.GetCreditLedgerRequest.SerializeToString,
            payment__pb2.GetCreditLedgerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AdminCreateCheckoutSession(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/AdminCreateCheckoutSession',
            payment__pb2.AdminCreateCheckoutSessionRequest.SerializeToString,
            payment__pb2.AdminCreateCheckoutSessionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateCustomerPortalSession(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/CreateCustomerPortalSession',
            payment__pb2.CreateCustomerPortalSessionRequest.SerializeToString,
            payment__pb2.CreateCustomerPortalSessionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSubscriptionDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/GetSubscriptionDetails',
            payment__pb2.GetSubscriptionDetailsRequest.SerializeToString,
            payment__pb2.GetSubscriptionDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/CancelSubscription',
            payment__pb2.CancelSubscriptionRequest.SerializeToString,
            payment__pb2.CancelSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
