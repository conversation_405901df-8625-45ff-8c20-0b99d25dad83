# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import analytics_pb2 as analytics__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in analytics_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AnalyticsServiceStub(object):
    """Analytics Service definition
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.TrackEvent = channel.unary_unary(
                '/analytics.AnalyticsService/TrackEvent',
                request_serializer=analytics__pb2.TrackEventRequest.SerializeToString,
                response_deserializer=analytics__pb2.TrackEventResponse.FromString,
                _registered_method=True)
        self.GetServiceMetrics = channel.unary_unary(
                '/analytics.AnalyticsService/GetServiceMetrics',
                request_serializer=analytics__pb2.GetServiceMetricsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetServiceMetricsResponse.FromString,
                _registered_method=True)
        self.GetUserActivity = channel.unary_unary(
                '/analytics.AnalyticsService/GetUserActivity',
                request_serializer=analytics__pb2.GetUserActivityRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetUserActivityResponse.FromString,
                _registered_method=True)
        self.GetRatingAnalytics = channel.unary_unary(
                '/analytics.AnalyticsService/GetRatingAnalytics',
                request_serializer=analytics__pb2.GetRatingAnalyticsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetRatingAnalyticsResponse.FromString,
                _registered_method=True)
        self.GetOverviewAnalytics = channel.unary_unary(
                '/analytics.AnalyticsService/GetOverviewAnalytics',
                request_serializer=analytics__pb2.GetOverviewAnalyticsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetOverviewAnalyticsResponse.FromString,
                _registered_method=True)
        self.TrackActivation = channel.unary_unary(
                '/analytics.AnalyticsService/TrackActivation',
                request_serializer=analytics__pb2.TrackActivationRequest.SerializeToString,
                response_deserializer=analytics__pb2.TrackActivationResponse.FromString,
                _registered_method=True)
        self.GetActivationMetrics = channel.unary_unary(
                '/analytics.AnalyticsService/GetActivationMetrics',
                request_serializer=analytics__pb2.GetActivationMetricsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetActivationMetricsResponse.FromString,
                _registered_method=True)
        self.CreateWebhook = channel.unary_unary(
                '/analytics.AnalyticsService/CreateWebhook',
                request_serializer=analytics__pb2.CreateWebhookRequest.SerializeToString,
                response_deserializer=analytics__pb2.CreateWebhookResponse.FromString,
                _registered_method=True)
        self.GetWebhooks = channel.unary_unary(
                '/analytics.AnalyticsService/GetWebhooks',
                request_serializer=analytics__pb2.GetWebhooksRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetWebhooksResponse.FromString,
                _registered_method=True)
        self.UpdateWebhook = channel.unary_unary(
                '/analytics.AnalyticsService/UpdateWebhook',
                request_serializer=analytics__pb2.UpdateWebhookRequest.SerializeToString,
                response_deserializer=analytics__pb2.UpdateWebhookResponse.FromString,
                _registered_method=True)
        self.DeleteWebhook = channel.unary_unary(
                '/analytics.AnalyticsService/DeleteWebhook',
                request_serializer=analytics__pb2.DeleteWebhookRequest.SerializeToString,
                response_deserializer=analytics__pb2.DeleteWebhookResponse.FromString,
                _registered_method=True)
        self.GetWebhookLogs = channel.unary_unary(
                '/analytics.AnalyticsService/GetWebhookLogs',
                request_serializer=analytics__pb2.GetWebhookLogsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetWebhookLogsResponse.FromString,
                _registered_method=True)
        self.GetDashboardOverview = channel.unary_unary(
                '/analytics.AnalyticsService/GetDashboardOverview',
                request_serializer=analytics__pb2.GetDashboardOverviewRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetDashboardOverviewResponse.FromString,
                _registered_method=True)
        self.GetDashboardMetrics = channel.unary_unary(
                '/analytics.AnalyticsService/GetDashboardMetrics',
                request_serializer=analytics__pb2.GetDashboardMetricsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetDashboardMetricsResponse.FromString,
                _registered_method=True)
        self.GetCreditUsageBreakdown = channel.unary_unary(
                '/analytics.AnalyticsService/GetCreditUsageBreakdown',
                request_serializer=analytics__pb2.GetCreditUsageBreakdownRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetCreditUsageBreakdownResponse.FromString,
                _registered_method=True)
        self.GetAppCreditUsage = channel.unary_unary(
                '/analytics.AnalyticsService/GetAppCreditUsage',
                request_serializer=analytics__pb2.GetAppCreditUsageRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetAppCreditUsageResponse.FromString,
                _registered_method=True)
        self.GetLatestApiRequests = channel.unary_unary(
                '/analytics.AnalyticsService/GetLatestApiRequests',
                request_serializer=analytics__pb2.GetLatestApiRequestsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetLatestApiRequestsResponse.FromString,
                _registered_method=True)
        self.GetAgentPerformance = channel.unary_unary(
                '/analytics.AnalyticsService/GetAgentPerformance',
                request_serializer=analytics__pb2.GetAgentPerformanceRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetAgentPerformanceResponse.FromString,
                _registered_method=True)
        self.GetWorkflowUtilization = channel.unary_unary(
                '/analytics.AnalyticsService/GetWorkflowUtilization',
                request_serializer=analytics__pb2.GetWorkflowUtilizationRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetWorkflowUtilizationResponse.FromString,
                _registered_method=True)
        self.GetSystemActivity = channel.unary_unary(
                '/analytics.AnalyticsService/GetSystemActivity',
                request_serializer=analytics__pb2.GetSystemActivityRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetSystemActivityResponse.FromString,
                _registered_method=True)
        self.RecordApiRequest = channel.unary_unary(
                '/analytics.AnalyticsService/RecordApiRequest',
                request_serializer=analytics__pb2.RecordApiRequestRequest.SerializeToString,
                response_deserializer=analytics__pb2.RecordApiRequestResponse.FromString,
                _registered_method=True)
        self.RecordSystemActivity = channel.unary_unary(
                '/analytics.AnalyticsService/RecordSystemActivity',
                request_serializer=analytics__pb2.RecordSystemActivityRequest.SerializeToString,
                response_deserializer=analytics__pb2.RecordSystemActivityResponse.FromString,
                _registered_method=True)


class AnalyticsServiceServicer(object):
    """Analytics Service definition
    """

    def TrackEvent(self, request, context):
        """Track an analytics event
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetServiceMetrics(self, request, context):
        """Get metrics for a specific service
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetUserActivity(self, request, context):
        """Get user activity data
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRatingAnalytics(self, request, context):
        """Get rating analytics
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOverviewAnalytics(self, request, context):
        """Overview Analytics - Dashboard functionality
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TrackActivation(self, request, context):
        """Activate Functionality
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActivationMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateWebhook(self, request, context):
        """Webhook Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWebhooks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateWebhook(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteWebhook(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWebhookLogs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDashboardOverview(self, request, context):
        """Dashboard Overview - Main analytics dashboard
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDashboardMetrics(self, request, context):
        """Enhanced Dashboard Analytics - New comprehensive dashboard functionality
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCreditUsageBreakdown(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAppCreditUsage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLatestApiRequests(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAgentPerformance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWorkflowUtilization(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSystemActivity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RecordApiRequest(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RecordSystemActivity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AnalyticsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'TrackEvent': grpc.unary_unary_rpc_method_handler(
                    servicer.TrackEvent,
                    request_deserializer=analytics__pb2.TrackEventRequest.FromString,
                    response_serializer=analytics__pb2.TrackEventResponse.SerializeToString,
            ),
            'GetServiceMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetServiceMetrics,
                    request_deserializer=analytics__pb2.GetServiceMetricsRequest.FromString,
                    response_serializer=analytics__pb2.GetServiceMetricsResponse.SerializeToString,
            ),
            'GetUserActivity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUserActivity,
                    request_deserializer=analytics__pb2.GetUserActivityRequest.FromString,
                    response_serializer=analytics__pb2.GetUserActivityResponse.SerializeToString,
            ),
            'GetRatingAnalytics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRatingAnalytics,
                    request_deserializer=analytics__pb2.GetRatingAnalyticsRequest.FromString,
                    response_serializer=analytics__pb2.GetRatingAnalyticsResponse.SerializeToString,
            ),
            'GetOverviewAnalytics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOverviewAnalytics,
                    request_deserializer=analytics__pb2.GetOverviewAnalyticsRequest.FromString,
                    response_serializer=analytics__pb2.GetOverviewAnalyticsResponse.SerializeToString,
            ),
            'TrackActivation': grpc.unary_unary_rpc_method_handler(
                    servicer.TrackActivation,
                    request_deserializer=analytics__pb2.TrackActivationRequest.FromString,
                    response_serializer=analytics__pb2.TrackActivationResponse.SerializeToString,
            ),
            'GetActivationMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActivationMetrics,
                    request_deserializer=analytics__pb2.GetActivationMetricsRequest.FromString,
                    response_serializer=analytics__pb2.GetActivationMetricsResponse.SerializeToString,
            ),
            'CreateWebhook': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateWebhook,
                    request_deserializer=analytics__pb2.CreateWebhookRequest.FromString,
                    response_serializer=analytics__pb2.CreateWebhookResponse.SerializeToString,
            ),
            'GetWebhooks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWebhooks,
                    request_deserializer=analytics__pb2.GetWebhooksRequest.FromString,
                    response_serializer=analytics__pb2.GetWebhooksResponse.SerializeToString,
            ),
            'UpdateWebhook': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateWebhook,
                    request_deserializer=analytics__pb2.UpdateWebhookRequest.FromString,
                    response_serializer=analytics__pb2.UpdateWebhookResponse.SerializeToString,
            ),
            'DeleteWebhook': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteWebhook,
                    request_deserializer=analytics__pb2.DeleteWebhookRequest.FromString,
                    response_serializer=analytics__pb2.DeleteWebhookResponse.SerializeToString,
            ),
            'GetWebhookLogs': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWebhookLogs,
                    request_deserializer=analytics__pb2.GetWebhookLogsRequest.FromString,
                    response_serializer=analytics__pb2.GetWebhookLogsResponse.SerializeToString,
            ),
            'GetDashboardOverview': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDashboardOverview,
                    request_deserializer=analytics__pb2.GetDashboardOverviewRequest.FromString,
                    response_serializer=analytics__pb2.GetDashboardOverviewResponse.SerializeToString,
            ),
            'GetDashboardMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDashboardMetrics,
                    request_deserializer=analytics__pb2.GetDashboardMetricsRequest.FromString,
                    response_serializer=analytics__pb2.GetDashboardMetricsResponse.SerializeToString,
            ),
            'GetCreditUsageBreakdown': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCreditUsageBreakdown,
                    request_deserializer=analytics__pb2.GetCreditUsageBreakdownRequest.FromString,
                    response_serializer=analytics__pb2.GetCreditUsageBreakdownResponse.SerializeToString,
            ),
            'GetAppCreditUsage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAppCreditUsage,
                    request_deserializer=analytics__pb2.GetAppCreditUsageRequest.FromString,
                    response_serializer=analytics__pb2.GetAppCreditUsageResponse.SerializeToString,
            ),
            'GetLatestApiRequests': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLatestApiRequests,
                    request_deserializer=analytics__pb2.GetLatestApiRequestsRequest.FromString,
                    response_serializer=analytics__pb2.GetLatestApiRequestsResponse.SerializeToString,
            ),
            'GetAgentPerformance': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAgentPerformance,
                    request_deserializer=analytics__pb2.GetAgentPerformanceRequest.FromString,
                    response_serializer=analytics__pb2.GetAgentPerformanceResponse.SerializeToString,
            ),
            'GetWorkflowUtilization': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWorkflowUtilization,
                    request_deserializer=analytics__pb2.GetWorkflowUtilizationRequest.FromString,
                    response_serializer=analytics__pb2.GetWorkflowUtilizationResponse.SerializeToString,
            ),
            'GetSystemActivity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSystemActivity,
                    request_deserializer=analytics__pb2.GetSystemActivityRequest.FromString,
                    response_serializer=analytics__pb2.GetSystemActivityResponse.SerializeToString,
            ),
            'RecordApiRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.RecordApiRequest,
                    request_deserializer=analytics__pb2.RecordApiRequestRequest.FromString,
                    response_serializer=analytics__pb2.RecordApiRequestResponse.SerializeToString,
            ),
            'RecordSystemActivity': grpc.unary_unary_rpc_method_handler(
                    servicer.RecordSystemActivity,
                    request_deserializer=analytics__pb2.RecordSystemActivityRequest.FromString,
                    response_serializer=analytics__pb2.RecordSystemActivityResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'analytics.AnalyticsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('analytics.AnalyticsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AnalyticsService(object):
    """Analytics Service definition
    """

    @staticmethod
    def TrackEvent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/TrackEvent',
            analytics__pb2.TrackEventRequest.SerializeToString,
            analytics__pb2.TrackEventResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetServiceMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetServiceMetrics',
            analytics__pb2.GetServiceMetricsRequest.SerializeToString,
            analytics__pb2.GetServiceMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetUserActivity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetUserActivity',
            analytics__pb2.GetUserActivityRequest.SerializeToString,
            analytics__pb2.GetUserActivityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRatingAnalytics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetRatingAnalytics',
            analytics__pb2.GetRatingAnalyticsRequest.SerializeToString,
            analytics__pb2.GetRatingAnalyticsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOverviewAnalytics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetOverviewAnalytics',
            analytics__pb2.GetOverviewAnalyticsRequest.SerializeToString,
            analytics__pb2.GetOverviewAnalyticsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TrackActivation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/TrackActivation',
            analytics__pb2.TrackActivationRequest.SerializeToString,
            analytics__pb2.TrackActivationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetActivationMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetActivationMetrics',
            analytics__pb2.GetActivationMetricsRequest.SerializeToString,
            analytics__pb2.GetActivationMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateWebhook(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/CreateWebhook',
            analytics__pb2.CreateWebhookRequest.SerializeToString,
            analytics__pb2.CreateWebhookResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetWebhooks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetWebhooks',
            analytics__pb2.GetWebhooksRequest.SerializeToString,
            analytics__pb2.GetWebhooksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateWebhook(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/UpdateWebhook',
            analytics__pb2.UpdateWebhookRequest.SerializeToString,
            analytics__pb2.UpdateWebhookResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteWebhook(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/DeleteWebhook',
            analytics__pb2.DeleteWebhookRequest.SerializeToString,
            analytics__pb2.DeleteWebhookResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetWebhookLogs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetWebhookLogs',
            analytics__pb2.GetWebhookLogsRequest.SerializeToString,
            analytics__pb2.GetWebhookLogsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDashboardOverview(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetDashboardOverview',
            analytics__pb2.GetDashboardOverviewRequest.SerializeToString,
            analytics__pb2.GetDashboardOverviewResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDashboardMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetDashboardMetrics',
            analytics__pb2.GetDashboardMetricsRequest.SerializeToString,
            analytics__pb2.GetDashboardMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCreditUsageBreakdown(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetCreditUsageBreakdown',
            analytics__pb2.GetCreditUsageBreakdownRequest.SerializeToString,
            analytics__pb2.GetCreditUsageBreakdownResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAppCreditUsage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetAppCreditUsage',
            analytics__pb2.GetAppCreditUsageRequest.SerializeToString,
            analytics__pb2.GetAppCreditUsageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLatestApiRequests(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetLatestApiRequests',
            analytics__pb2.GetLatestApiRequestsRequest.SerializeToString,
            analytics__pb2.GetLatestApiRequestsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAgentPerformance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetAgentPerformance',
            analytics__pb2.GetAgentPerformanceRequest.SerializeToString,
            analytics__pb2.GetAgentPerformanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetWorkflowUtilization(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetWorkflowUtilization',
            analytics__pb2.GetWorkflowUtilizationRequest.SerializeToString,
            analytics__pb2.GetWorkflowUtilizationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSystemActivity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/GetSystemActivity',
            analytics__pb2.GetSystemActivityRequest.SerializeToString,
            analytics__pb2.GetSystemActivityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RecordApiRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/RecordApiRequest',
            analytics__pb2.RecordApiRequestRequest.SerializeToString,
            analytics__pb2.RecordApiRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RecordSystemActivity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.AnalyticsService/RecordSystemActivity',
            analytics__pb2.RecordSystemActivityRequest.SerializeToString,
            analytics__pb2.RecordSystemActivityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class ApplicationServiceStub(object):
    """Application Management Service definition
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateApplication = channel.unary_unary(
                '/analytics.ApplicationService/CreateApplication',
                request_serializer=analytics__pb2.CreateApplicationRequest.SerializeToString,
                response_deserializer=analytics__pb2.CreateApplicationResponse.FromString,
                _registered_method=True)
        self.GetApplications = channel.unary_unary(
                '/analytics.ApplicationService/GetApplications',
                request_serializer=analytics__pb2.GetApplicationsRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetApplicationsResponse.FromString,
                _registered_method=True)
        self.GetApplication = channel.unary_unary(
                '/analytics.ApplicationService/GetApplication',
                request_serializer=analytics__pb2.GetApplicationRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetApplicationResponse.FromString,
                _registered_method=True)
        self.UpdateApplication = channel.unary_unary(
                '/analytics.ApplicationService/UpdateApplication',
                request_serializer=analytics__pb2.UpdateApplicationRequest.SerializeToString,
                response_deserializer=analytics__pb2.UpdateApplicationResponse.FromString,
                _registered_method=True)
        self.DeleteApplication = channel.unary_unary(
                '/analytics.ApplicationService/DeleteApplication',
                request_serializer=analytics__pb2.DeleteApplicationRequest.SerializeToString,
                response_deserializer=analytics__pb2.DeleteApplicationResponse.FromString,
                _registered_method=True)
        self.AttachImageToApplication = channel.unary_unary(
                '/analytics.ApplicationService/AttachImageToApplication',
                request_serializer=analytics__pb2.AttachImageToApplicationRequest.SerializeToString,
                response_deserializer=analytics__pb2.AttachImageToApplicationResponse.FromString,
                _registered_method=True)


class ApplicationServiceServicer(object):
    """Application Management Service definition
    """

    def CreateApplication(self, request, context):
        """Application Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetApplications(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetApplication(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateApplication(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteApplication(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AttachImageToApplication(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ApplicationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateApplication': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateApplication,
                    request_deserializer=analytics__pb2.CreateApplicationRequest.FromString,
                    response_serializer=analytics__pb2.CreateApplicationResponse.SerializeToString,
            ),
            'GetApplications': grpc.unary_unary_rpc_method_handler(
                    servicer.GetApplications,
                    request_deserializer=analytics__pb2.GetApplicationsRequest.FromString,
                    response_serializer=analytics__pb2.GetApplicationsResponse.SerializeToString,
            ),
            'GetApplication': grpc.unary_unary_rpc_method_handler(
                    servicer.GetApplication,
                    request_deserializer=analytics__pb2.GetApplicationRequest.FromString,
                    response_serializer=analytics__pb2.GetApplicationResponse.SerializeToString,
            ),
            'UpdateApplication': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateApplication,
                    request_deserializer=analytics__pb2.UpdateApplicationRequest.FromString,
                    response_serializer=analytics__pb2.UpdateApplicationResponse.SerializeToString,
            ),
            'DeleteApplication': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteApplication,
                    request_deserializer=analytics__pb2.DeleteApplicationRequest.FromString,
                    response_serializer=analytics__pb2.DeleteApplicationResponse.SerializeToString,
            ),
            'AttachImageToApplication': grpc.unary_unary_rpc_method_handler(
                    servicer.AttachImageToApplication,
                    request_deserializer=analytics__pb2.AttachImageToApplicationRequest.FromString,
                    response_serializer=analytics__pb2.AttachImageToApplicationResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'analytics.ApplicationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('analytics.ApplicationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ApplicationService(object):
    """Application Management Service definition
    """

    @staticmethod
    def CreateApplication(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ApplicationService/CreateApplication',
            analytics__pb2.CreateApplicationRequest.SerializeToString,
            analytics__pb2.CreateApplicationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetApplications(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ApplicationService/GetApplications',
            analytics__pb2.GetApplicationsRequest.SerializeToString,
            analytics__pb2.GetApplicationsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetApplication(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ApplicationService/GetApplication',
            analytics__pb2.GetApplicationRequest.SerializeToString,
            analytics__pb2.GetApplicationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateApplication(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ApplicationService/UpdateApplication',
            analytics__pb2.UpdateApplicationRequest.SerializeToString,
            analytics__pb2.UpdateApplicationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteApplication(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ApplicationService/DeleteApplication',
            analytics__pb2.DeleteApplicationRequest.SerializeToString,
            analytics__pb2.DeleteApplicationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AttachImageToApplication(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ApplicationService/AttachImageToApplication',
            analytics__pb2.AttachImageToApplicationRequest.SerializeToString,
            analytics__pb2.AttachImageToApplicationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class ActivityServiceStub(object):
    """Service definition for managing Activities
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateActivity = channel.unary_unary(
                '/analytics.ActivityService/CreateActivity',
                request_serializer=analytics__pb2.CreateActivityRequest.SerializeToString,
                response_deserializer=analytics__pb2.CreateActivityResponse.FromString,
                _registered_method=True)
        self.UpdateActivity = channel.unary_unary(
                '/analytics.ActivityService/UpdateActivity',
                request_serializer=analytics__pb2.UpdateActivityRequest.SerializeToString,
                response_deserializer=analytics__pb2.UpdateActivityResponse.FromString,
                _registered_method=True)
        self.DeleteActivity = channel.unary_unary(
                '/analytics.ActivityService/DeleteActivity',
                request_serializer=analytics__pb2.DeleteActivityRequest.SerializeToString,
                response_deserializer=analytics__pb2.DeleteActivityResponse.FromString,
                _registered_method=True)
        self.CreateActivityLog = channel.unary_unary(
                '/analytics.ActivityService/CreateActivityLog',
                request_serializer=analytics__pb2.CreateActivityLogRequest.SerializeToString,
                response_deserializer=analytics__pb2.ActivityLog.FromString,
                _registered_method=True)
        self.CreateActivityEvent = channel.unary_unary(
                '/analytics.ActivityService/CreateActivityEvent',
                request_serializer=analytics__pb2.CreateActivityEventRequest.SerializeToString,
                response_deserializer=analytics__pb2.CreateActivityEventResponse.FromString,
                _registered_method=True)
        self.GetActivity = channel.unary_unary(
                '/analytics.ActivityService/GetActivity',
                request_serializer=analytics__pb2.GetActivityRequest.SerializeToString,
                response_deserializer=analytics__pb2.GetActivityResponse.FromString,
                _registered_method=True)
        self.ListActivities = channel.unary_unary(
                '/analytics.ActivityService/ListActivities',
                request_serializer=analytics__pb2.ListActivitiesRequest.SerializeToString,
                response_deserializer=analytics__pb2.ListActivitiesResponse.FromString,
                _registered_method=True)
        self.ListActivityLogs = channel.unary_unary(
                '/analytics.ActivityService/ListActivityLogs',
                request_serializer=analytics__pb2.ListActivityLogsRequest.SerializeToString,
                response_deserializer=analytics__pb2.ListActivityLogsResponse.FromString,
                _registered_method=True)
        self.ListActivityEvents = channel.unary_unary(
                '/analytics.ActivityService/ListActivityEvents',
                request_serializer=analytics__pb2.ListActivityEventsRequest.SerializeToString,
                response_deserializer=analytics__pb2.ListActivityEventsResponse.FromString,
                _registered_method=True)


class ActivityServiceServicer(object):
    """Service definition for managing Activities
    """

    def CreateActivity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateActivity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteActivity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateActivityLog(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateActivityEvent(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActivity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListActivities(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListActivityLogs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListActivityEvents(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ActivityServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateActivity': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateActivity,
                    request_deserializer=analytics__pb2.CreateActivityRequest.FromString,
                    response_serializer=analytics__pb2.CreateActivityResponse.SerializeToString,
            ),
            'UpdateActivity': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateActivity,
                    request_deserializer=analytics__pb2.UpdateActivityRequest.FromString,
                    response_serializer=analytics__pb2.UpdateActivityResponse.SerializeToString,
            ),
            'DeleteActivity': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteActivity,
                    request_deserializer=analytics__pb2.DeleteActivityRequest.FromString,
                    response_serializer=analytics__pb2.DeleteActivityResponse.SerializeToString,
            ),
            'CreateActivityLog': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateActivityLog,
                    request_deserializer=analytics__pb2.CreateActivityLogRequest.FromString,
                    response_serializer=analytics__pb2.ActivityLog.SerializeToString,
            ),
            'CreateActivityEvent': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateActivityEvent,
                    request_deserializer=analytics__pb2.CreateActivityEventRequest.FromString,
                    response_serializer=analytics__pb2.CreateActivityEventResponse.SerializeToString,
            ),
            'GetActivity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActivity,
                    request_deserializer=analytics__pb2.GetActivityRequest.FromString,
                    response_serializer=analytics__pb2.GetActivityResponse.SerializeToString,
            ),
            'ListActivities': grpc.unary_unary_rpc_method_handler(
                    servicer.ListActivities,
                    request_deserializer=analytics__pb2.ListActivitiesRequest.FromString,
                    response_serializer=analytics__pb2.ListActivitiesResponse.SerializeToString,
            ),
            'ListActivityLogs': grpc.unary_unary_rpc_method_handler(
                    servicer.ListActivityLogs,
                    request_deserializer=analytics__pb2.ListActivityLogsRequest.FromString,
                    response_serializer=analytics__pb2.ListActivityLogsResponse.SerializeToString,
            ),
            'ListActivityEvents': grpc.unary_unary_rpc_method_handler(
                    servicer.ListActivityEvents,
                    request_deserializer=analytics__pb2.ListActivityEventsRequest.FromString,
                    response_serializer=analytics__pb2.ListActivityEventsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'analytics.ActivityService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('analytics.ActivityService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ActivityService(object):
    """Service definition for managing Activities
    """

    @staticmethod
    def CreateActivity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/CreateActivity',
            analytics__pb2.CreateActivityRequest.SerializeToString,
            analytics__pb2.CreateActivityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateActivity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/UpdateActivity',
            analytics__pb2.UpdateActivityRequest.SerializeToString,
            analytics__pb2.UpdateActivityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteActivity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/DeleteActivity',
            analytics__pb2.DeleteActivityRequest.SerializeToString,
            analytics__pb2.DeleteActivityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateActivityLog(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/CreateActivityLog',
            analytics__pb2.CreateActivityLogRequest.SerializeToString,
            analytics__pb2.ActivityLog.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateActivityEvent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/CreateActivityEvent',
            analytics__pb2.CreateActivityEventRequest.SerializeToString,
            analytics__pb2.CreateActivityEventResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetActivity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/GetActivity',
            analytics__pb2.GetActivityRequest.SerializeToString,
            analytics__pb2.GetActivityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListActivities(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/ListActivities',
            analytics__pb2.ListActivitiesRequest.SerializeToString,
            analytics__pb2.ListActivitiesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListActivityLogs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/ListActivityLogs',
            analytics__pb2.ListActivityLogsRequest.SerializeToString,
            analytics__pb2.ListActivityLogsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListActivityEvents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/analytics.ActivityService/ListActivityEvents',
            analytics__pb2.ListActivityEventsRequest.SerializeToString,
            analytics__pb2.ListActivityEventsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
