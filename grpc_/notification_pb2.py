# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: notification.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'notification.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12notification.proto\x12\rnotifications\"x\n\x0cNotification\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x0c\n\x04link\x18\x04 \x01(\t\x12\x0c\n\x04logo\x18\x05 \x01(\t\x12\x0c\n\x04seen\x18\x06 \x01(\x08\x12\x12\n\ncreated_at\x18\x07 \x01(\t\"O\n\x1bGetUserNotificationsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\"\x84\x01\n\x1cGetUserNotificationsResponse\x12\x32\n\rnotifications\x18\x01 \x03(\x0b\x32\x1b.notifications.Notification\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x04 \x01(\x05\"I\n\x1dMarkNotificationAsSeenRequest\x12\x17\n\x0fnotification_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"B\n\x1eMarkNotificationAsSeenResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"F\n\x1aGetNotificationByIdRequest\x12\x17\n\x0fnotification_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"r\n\x1bGetNotificationByIdResponse\x12\x31\n\x0cnotification\x18\x01 \x01(\x0b\x32\x1b.notifications.Notification\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x0f\n\x07message\x18\x03 \x01(\t2\xeb\x02\n\x13NotificationService\x12o\n\x14GetUserNotifications\x12*.notifications.GetUserNotificationsRequest\x1a+.notifications.GetUserNotificationsResponse\x12u\n\x16MarkNotificationAsSeen\x12,.notifications.MarkNotificationAsSeenRequest\x1a-.notifications.MarkNotificationAsSeenResponse\x12l\n\x13GetNotificationById\x12).notifications.GetNotificationByIdRequest\x1a*.notifications.GetNotificationByIdResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'notification_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_NOTIFICATION']._serialized_start=37
  _globals['_NOTIFICATION']._serialized_end=157
  _globals['_GETUSERNOTIFICATIONSREQUEST']._serialized_start=159
  _globals['_GETUSERNOTIFICATIONSREQUEST']._serialized_end=238
  _globals['_GETUSERNOTIFICATIONSRESPONSE']._serialized_start=241
  _globals['_GETUSERNOTIFICATIONSRESPONSE']._serialized_end=373
  _globals['_MARKNOTIFICATIONASSEENREQUEST']._serialized_start=375
  _globals['_MARKNOTIFICATIONASSEENREQUEST']._serialized_end=448
  _globals['_MARKNOTIFICATIONASSEENRESPONSE']._serialized_start=450
  _globals['_MARKNOTIFICATIONASSEENRESPONSE']._serialized_end=516
  _globals['_GETNOTIFICATIONBYIDREQUEST']._serialized_start=518
  _globals['_GETNOTIFICATIONBYIDREQUEST']._serialized_end=588
  _globals['_GETNOTIFICATIONBYIDRESPONSE']._serialized_start=590
  _globals['_GETNOTIFICATIONBYIDRESPONSE']._serialized_end=704
  _globals['_NOTIFICATIONSERVICE']._serialized_start=707
  _globals['_NOTIFICATIONSERVICE']._serialized_end=1070
# @@protoc_insertion_point(module_scope)
