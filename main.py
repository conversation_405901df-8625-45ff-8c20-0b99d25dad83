"""
Payment Service Main Application
Combined gRPC and HTTP server for handling Stripe payments and subscription management.
"""

import asyncio
import signal
import sys
from concurrent.futures import ThreadPoolExecutor
from contextlib import asynccontextmanager

import grpc
import structlog
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.api.health import router as health_router
from app.api.checkout import router as checkout_router
from app.api.webhooks import router as webhook_router
from app.db.session import create_tables

# gRPC imports
from app.grpc import payment_pb2_grpc # Assuming generated stubs are in app/grpc/
from app.services.payment_grpc_service import PaymentGRPCService

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Payment service starting up", version="0.1.0")

    # Create database tables
    create_tables()

    yield

    # Shutdown
    logger.info("Payment service shutting down")


# Create FastAPI application
app = FastAPI(
    title="Payment Service",
    description="Microservice for handling Stripe payments and subscription management",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health_router, prefix="/health", tags=["health"])
app.include_router(checkout_router, tags=["checkout"])
app.include_router(webhook_router, tags=["webhooks"])


async def serve_grpc():
    """Start gRPC server."""
    server = grpc.aio.server(ThreadPoolExecutor(max_workers=settings.GRPC_MAX_WORKERS or 10))
    
    # Instantiate your servicer
    # Note: If PaymentGRPCService requires dependencies (like DB session factory or other service clients),
    # they would need to be initialized and passed here.
    payment_servicer = PaymentGRPCService()
    payment_pb2_grpc.add_PaymentServiceServicer_to_server(payment_servicer, server)
    
    listen_addr = f"[::]:{settings.GRPC_PORT or 50051}" # Use settings.GRPC_PORT or a default
    server.add_insecure_port(listen_addr)
    logger.info("Starting gRPC server", address=listen_addr)
    await server.start()
    
    try:
        await server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("gRPC server stopping due to KeyboardInterrupt")
        await server.stop(0)
    except Exception as e:
        logger.error("gRPC server error", error=str(e))
        await server.stop(0)
    finally:
        logger.info("gRPC server stopped.")


async def serve_http():
    """Start HTTP server."""
    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=settings.HTTP_PORT,
        log_level="info",
        reload=settings.DEBUG,
    )
    server = uvicorn.Server(config)
    logger.info("Starting HTTP server", address=f"0.0.0.0:{settings.HTTP_PORT}")
    await server.serve()


async def main():
    """Main application entry point."""
    # Handle graceful shutdown
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal", signal=signum)
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Run both servers concurrently
    await asyncio.gather(
        serve_grpc(),
        serve_http(),
        return_exceptions=True
    )


if __name__ == "__main__":
    asyncio.run(main())
