import json
import uuid
import time
from confluent_kafka import Producer
from app.core.config import settings
from app.schemas.payment import TokenUsageEvent
from datetime import datetime

def get_kafka_producer():
    """Creates and returns a Kafka producer."""
    producer_conf = {
        'bootstrap.servers': settings.KAFKA_BOOTSTRAP_SERVERS,
    }
    return Producer(producer_conf)

def delivery_report(err, msg):
    """ Called once for each message produced to indicate delivery result. """
    if err is not None:
        print(f'Message delivery failed: {err}')
    else:
        print(f'Message delivered to {msg.topic()} [{msg.partition()}]')

def send_test_event():
    """Sends a single test token usage event to Kafka."""
    producer = get_kafka_producer()

    # --- Create a sample event ---
    # In a real test, you would use actual IDs from your database.
    # --- Create a sample event that mirrors DeductCreditsRequest ---
    # In a real test, you would use actual IDs from your database.
    test_event = TokenUsageEvent(
        event_id=uuid.uuid4(),
        organisation_id="790db989-a976-4229-9162-37cc7dfd270b", # Replace with a real org ID
        user_id="7b045240-05f2-41e1-b77a-5e464b0ae8be",       # Replace with a real user ID
        input_tokens=100,
        output_tokens=500,
        consumed_credits=0.025, # This should be pre-calculated by the producer
        timestamp=datetime.utcnow()
    )

    # Convert Pydantic model to a dictionary for JSON serialization
    # Use model_dump for Pydantic v2
    try:
        event_dict = test_event.model_dump()
    except AttributeError:
        event_dict = test_event.dict()


    # The UUID and datetime need to be converted to strings for JSON
    event_dict['event_id'] = str(event_dict['event_id'])
    event_dict['timestamp'] = event_dict['timestamp'].isoformat()


    event_value = json.dumps(event_dict)

    print(f"Sending event to topic '{settings.KAFKA_TOKEN_USAGE_TOPIC}':")
    print(event_value)

    # Asynchronously produce a message, the delivery report callback
    # will be triggered from poll() above, or flush().
    producer.produce(
        settings.KAFKA_TOKEN_USAGE_TOPIC,
        event_value.encode('utf-8'),
        callback=delivery_report
    )

    # Wait for any outstanding messages to be delivered and delivery reports
    # to be received.
    producer.flush()

if __name__ == '__main__':
    send_test_event()