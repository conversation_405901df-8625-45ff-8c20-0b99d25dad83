from kafka import KafkaProducer, KafkaConsumer
import json

KAFKA_BROKER_URL = 'localhost:9092'  # Change as per your Kafka setup
LOG_TOPIC = 'gateway-logs'

def get_kafka_producer():
    return KafkaProducer(
        bootstrap_servers=KAFKA_BROKER_URL,
        value_serializer=lambda v: json.dumps(v).encode('utf-8')
    )

def get_kafka_consumer(group_id='gateway-log-consumer'):
    return KafkaConsumer(
        LOG_TOPIC,
        bootstrap_servers=KAFKA_BROKER_URL,
        auto_offset_reset='earliest',
        enable_auto_commit=True,
        group_id=group_id,
        value_deserializer=lambda m: json.loads(m.decode('utf-8'))
    )
