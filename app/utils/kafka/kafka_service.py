from .kafka_client import get_kafka_consumer

# This consumer will listen to the 'gateway-logs' topic and print incoming log messages

def consume_gateway_logs():
    consumer = get_kafka_consumer()
    print('Starting Kafka log consumer...')
    for message in consumer:
        log = message.value
        print(f"Received log: {log}")

# If you want to run this as a script
if __name__ == "__main__":
    consume_gateway_logs()
