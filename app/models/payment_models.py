"""
Database models for the Payment Service.
"""

import enum
from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Boolean,
    Numeric,
    Text,
    ForeignKey,
    Date,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.sql import func
from sqlalchemy.types import Enum as SQLEnum

from app.db.base_class import Base


class PaymentPlanType(enum.Enum):
    """Defines the types of plans available."""
    FREE = "free"
    PRO = "pro"


class PaymentTransactionType(enum.Enum):
    """Defines the types of monetary transactions."""
    PLAN_PURCHASE = "plan_purchase"
    CREDIT_PURCHASE = "credit_purchase"
    REFUND = "refund"


class PaymentTransactionStatus(enum.Enum):
    """Defines the status of a monetary transaction."""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


class PaymentSubscriptionStatus(enum.Enum):
    """Defines the status of a subscription."""
    ACTIVE = "active"
    CANCELED = "canceled"
    INCOMPLETE = "incomplete"
    PAST_DUE = "past_due"
    TRIALING = "trialing"


class PaymentPlan(Base):
    """Model for subscription plans."""
    __tablename__ = "payment_plans"

    id = Column(Integer, primary_key=True, index=True)
    plan_id_code = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False)
    credit_amount = Column(Numeric(10, 2), nullable=False)
    price = Column(Numeric(10, 2), nullable=False, default=0.00)
    stripe_price_id = Column(String, nullable=True, index=True)
    is_default = Column(Boolean, default=False, nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<PaymentPlan(plan_id_code='{self.plan_id_code}', name='{self.name}')>"


class PaymentCustomer(Base):
    """Model to map an organisation to a Stripe Customer."""
    __tablename__ = "payment_customers"

    organisation_id = Column(String, primary_key=True, index=True)
    stripe_customer_id = Column(String, unique=True, index=True, nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<PaymentCustomer(organisation_id='{self.organisation_id}', stripe_customer_id='{self.stripe_customer_id}')>"


class PaymentSubscription(Base):
    """Model for organisation subscriptions."""
    __tablename__ = "payment_subscriptions"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    organisation_id = Column(String, ForeignKey("payment_customers.organisation_id"), index=True, nullable=False)
    plan_id = Column(Integer, ForeignKey("payment_plans.id"), index=True, nullable=False)
    stripe_subscription_id = Column(String, unique=True, index=True, nullable=False)
    status = Column(SQLEnum(PaymentSubscriptionStatus), nullable=False)
    subscription_credits = Column(Numeric(20, 10), nullable=False, default=0.0)
    current_credits = Column(Numeric(20, 10), nullable=False, default=0.0)

    current_period_start = Column(DateTime(timezone=True), nullable=True)
    current_period_end = Column(DateTime(timezone=True), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<PaymentSubscription(id='{self.id}', organisation_id='{self.organisation_id}', status='{self.status.value}')>"


class PaymentTransaction(Base):
    """Model for monetary transactions (e.g., plan purchases)."""
    __tablename__ = "payment_transactions"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    organisation_id = Column(String, ForeignKey("payment_customers.organisation_id"), index=True, nullable=False)
    transaction_type = Column(SQLEnum(PaymentTransactionType), nullable=False, index=True)
    status = Column(SQLEnum(PaymentTransactionStatus), nullable=False, default=PaymentTransactionStatus.COMPLETED)
    amount_currency = Column(Integer, nullable=False)  # In cents
    currency = Column(String, default="usd", nullable=False)
    description = Column(Text, nullable=True)
    stripe_charge_id = Column(String, nullable=True, index=True)
    stripe_invoice_id = Column(String, nullable=True, index=True)
    subscription_id = Column(PG_UUID(as_uuid=True), ForeignKey("payment_subscriptions.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<PaymentTransaction(id='{self.id}', org_id='{self.organisation_id}', type='{self.transaction_type.value}')>"


class CreditDeduction(Base):
    """Model for logging credit deductions."""
    __tablename__ = "credit_deductions"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    organisation_id = Column(String, ForeignKey("payment_customers.organisation_id"), index=True, nullable=False)
    user_id = Column(String, index=True, nullable=False)
    agent_id = Column(String, index=True, nullable=True)
    credits_deducted = Column(Numeric(10, 4), nullable=False)
    remaining_credits = Column(Numeric(20, 10), nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<CreditDeduction(id='{self.id}', org_id='{self.organisation_id}', credits='{self.credits_deducted}')>"


class TokenUsageLog(Base):
    """Model for daily aggregated token usage tracking."""

    __tablename__ = "token_usage_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, index=True, nullable=False)
    organisation_id = Column(
        String, ForeignKey("payment_customers.organisation_id"), index=True, nullable=False
    )
    input_tokens = Column(Integer, nullable=False, default=0)
    output_tokens = Column(Integer, nullable=False, default=0)
    input_cost = Column(Numeric(10, 6), nullable=False, default=0.0)
    output_cost = Column(Numeric(10, 6), nullable=False, default=0.0)
    total_cost = Column(Numeric(10, 6), nullable=False, default=0.0)
    total_credits = Column(Numeric(10, 4), nullable=False, default=0.0)
    date = Column(Date, nullable=False, index=True)

    __table_args__ = (
        UniqueConstraint("user_id", "organisation_id", "date", name="_user_org_date_uc"),
    )

    def __repr__(self):
        return f"<TokenUsageLog(user_id='{self.user_id}', date='{self.date}', total_cost='{self.total_cost}')>"


class PaymentWebhookEvent(Base):
    """Model to track Stripe webhook events for audit and idempotency."""
    __tablename__ = "payment_webhook_events"

    id = Column(String, primary_key=True)
    event_type = Column(String, nullable=False)
    processed = Column(Boolean, default=False)
    processing_attempts = Column(Integer, default=0)
    event_data = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<PaymentWebhookEvent(id='{self.id}', event_type='{self.event_type}', processed={self.processed})>"