# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: payment.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'payment.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rpayment.proto\x12\x07payment\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"9\n\x0fGenericResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\"5\n\x1a\x41\x63tivateDefaultPlanRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"\xaa\x01\n\x14\x44\x65\x64uctCreditsRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x10\n\x08\x61gent_id\x18\x03 \x01(\t\x12\x14\n\x0cinput_tokens\x18\x04 \x01(\x05\x12\x15\n\routput_tokens\x18\x05 \x01(\x05\x12\x14\n\x0creference_id\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\"d\n\x15\x44\x65\x64uctCreditsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12#\n\x1bnew_balance_after_deduction\x18\x03 \x01(\x01\"v\n\x1c\x43reateCheckoutSessionRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x14\n\x0cplan_id_code\x18\x02 \x01(\t\x12\x13\n\x0bsuccess_url\x18\x03 \x01(\t\x12\x12\n\ncancel_url\x18\x04 \x01(\t\"q\n\x1d\x43reateCheckoutSessionResponse\x12\x14\n\x0c\x63heckout_url\x18\x01 \x01(\t\x12\x12\n\nsession_id\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x15\n\rerror_message\x18\x04 \x01(\t\"Q\n\"CreateCustomerPortalSessionRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x12\n\nreturn_url\x18\x02 \x01(\t\"a\n#CreateCustomerPortalSessionResponse\x12\x12\n\nportal_url\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\":\n\x14StripeWebhookRequest\x12\x0f\n\x07payload\x18\x01 \x01(\t\x12\x11\n\tsignature\x18\x02 \x01(\t\"\xef\x01\n\x13SubscriptionDetails\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x14\n\x0cplan_id_code\x18\x03 \x01(\t\x12+\n\x06status\x18\x04 \x01(\x0e\x32\x1b.payment.SubscriptionStatus\x12\x38\n\x14\x63urrent_period_start\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x63urrent_period_end\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"1\n\x16GetSubscriptionRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"u\n\x17GetSubscriptionResponse\x12\x32\n\x0csubscription\x18\x01 \x01(\x0b\x32\x1c.payment.SubscriptionDetails\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\"4\n\x19\x43\x61ncelSubscriptionRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"2\n\x17GetCreditBalanceRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"Z\n\x18GetCreditBalanceResponse\x12\x16\n\x0e\x63redit_balance\x18\x01 \x01(\x01\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\"\xd2\x01\n\x12TokenUsageLogEntry\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x10\n\x08\x61gent_id\x18\x04 \x01(\t\x12\x14\n\x0cinput_tokens\x18\x05 \x01(\x05\x12\x15\n\routput_tokens\x18\x06 \x01(\x05\x12\x18\n\x10\x63redits_consumed\x18\x07 \x01(\x01\x12-\n\ttimestamp\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xb0\x01\n\x14GetTokenUsageRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x10\n\x08\x61gent_id\x18\x03 \x01(\t\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"v\n\x15GetTokenUsageResponse\x12\x35\n\x10token_usage_logs\x18\x01 \x03(\x0b\x32\x1b.payment.TokenUsageLogEntry\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\"\x89\x01\n\x04Plan\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x14\n\x0cplan_id_code\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x15\n\rcredit_amount\x18\x04 \x01(\x01\x12\r\n\x05price\x18\x05 \x01(\x01\x12\x17\n\x0fstripe_price_id\x18\x06 \x01(\t\x12\x12\n\nis_default\x18\x07 \x01(\x08\"\x8a\x01\n\x11\x43reatePlanRequest\x12\x14\n\x0cplan_id_code\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x15\n\rcredit_amount\x18\x03 \x01(\x01\x12\r\n\x05price\x18\x04 \x01(\x01\x12\x17\n\x0fstripe_price_id\x18\x05 \x01(\t\x12\x12\n\nis_default\x18\x06 \x01(\x08\"\x12\n\x10ListPlansRequest\"1\n\x11ListPlansResponse\x12\x1c\n\x05plans\x18\x01 \x03(\x0b\x32\r.payment.Plan*8\n\x08PlanType\x12\x19\n\x15PLAN_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04\x46REE\x10\x01\x12\x07\n\x03PRO\x10\x02*\xc1\x01\n\x15\x43reditTransactionType\x12\'\n#CREDIT_TRANSACTION_TYPE_UNSPECIFIED\x10\x00\x12\x16\n\x12INITIAL_ALLOCATION\x10\x01\x12\x13\n\x0fUSAGE_DEDUCTION\x10\x02\x12\x13\n\x0f\x43REDIT_PURCHASE\x10\x03\x12\x1a\n\x16PLAN_CHANGE_ADJUSTMENT\x10\x04\x12\x15\n\x11MANUAL_ADJUSTMENT\x10\x05\x12\n\n\x06REFUND\x10\x06*\x7f\n\x12SubscriptionStatus\x12#\n\x1fSUBSCRIPTION_STATUS_UNSPECIFIED\x10\x00\x12\n\n\x06\x41\x43TIVE\x10\x01\x12\x0c\n\x08\x43\x41NCELED\x10\x02\x12\x0e\n\nINCOMPLETE\x10\x03\x12\x0c\n\x08PAST_DUE\x10\x04\x12\x0c\n\x08TRIALING\x10\x05\x32\xb8\x07\n\x0ePaymentService\x12T\n\x13\x41\x63tivateDefaultPlan\x12#.payment.ActivateDefaultPlanRequest\x1a\x18.payment.GenericResponse\x12N\n\rDeductCredits\x12\x1d.payment.DeductCreditsRequest\x1a\x1e.payment.DeductCreditsResponse\x12\x66\n\x15\x43reateCheckoutSession\x12%.payment.CreateCheckoutSessionRequest\x1a&.payment.CreateCheckoutSessionResponse\x12x\n\x1b\x43reateCustomerPortalSession\x12+.payment.CreateCustomerPortalSessionRequest\x1a,.payment.CreateCustomerPortalSessionResponse\x12N\n\x13HandleStripeWebhook\x12\x1d.payment.StripeWebhookRequest\x1a\x18.payment.GenericResponse\x12T\n\x0fGetSubscription\x12\x1f.payment.GetSubscriptionRequest\x1a .payment.GetSubscriptionResponse\x12R\n\x12\x43\x61ncelSubscription\x12\".payment.CancelSubscriptionRequest\x1a\x18.payment.GenericResponse\x12W\n\x10GetCreditBalance\x12 .payment.GetCreditBalanceRequest\x1a!.payment.GetCreditBalanceResponse\x12N\n\rGetTokenUsage\x12\x1d.payment.GetTokenUsageRequest\x1a\x1e.payment.GetTokenUsageResponse\x12\x37\n\nCreatePlan\x12\x1a.payment.CreatePlanRequest\x1a\r.payment.Plan\x12\x42\n\tListPlans\x12\x19.payment.ListPlansRequest\x1a\x1a.payment.ListPlansResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'payment_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PLANTYPE']._serialized_start=2429
  _globals['_PLANTYPE']._serialized_end=2485
  _globals['_CREDITTRANSACTIONTYPE']._serialized_start=2488
  _globals['_CREDITTRANSACTIONTYPE']._serialized_end=2681
  _globals['_SUBSCRIPTIONSTATUS']._serialized_start=2683
  _globals['_SUBSCRIPTIONSTATUS']._serialized_end=2810
  _globals['_GENERICRESPONSE']._serialized_start=89
  _globals['_GENERICRESPONSE']._serialized_end=146
  _globals['_ACTIVATEDEFAULTPLANREQUEST']._serialized_start=148
  _globals['_ACTIVATEDEFAULTPLANREQUEST']._serialized_end=201
  _globals['_DEDUCTCREDITSREQUEST']._serialized_start=204
  _globals['_DEDUCTCREDITSREQUEST']._serialized_end=374
  _globals['_DEDUCTCREDITSRESPONSE']._serialized_start=376
  _globals['_DEDUCTCREDITSRESPONSE']._serialized_end=476
  _globals['_CREATECHECKOUTSESSIONREQUEST']._serialized_start=478
  _globals['_CREATECHECKOUTSESSIONREQUEST']._serialized_end=596
  _globals['_CREATECHECKOUTSESSIONRESPONSE']._serialized_start=598
  _globals['_CREATECHECKOUTSESSIONRESPONSE']._serialized_end=711
  _globals['_CREATECUSTOMERPORTALSESSIONREQUEST']._serialized_start=713
  _globals['_CREATECUSTOMERPORTALSESSIONREQUEST']._serialized_end=794
  _globals['_CREATECUSTOMERPORTALSESSIONRESPONSE']._serialized_start=796
  _globals['_CREATECUSTOMERPORTALSESSIONRESPONSE']._serialized_end=893
  _globals['_STRIPEWEBHOOKREQUEST']._serialized_start=895
  _globals['_STRIPEWEBHOOKREQUEST']._serialized_end=953
  _globals['_SUBSCRIPTIONDETAILS']._serialized_start=956
  _globals['_SUBSCRIPTIONDETAILS']._serialized_end=1195
  _globals['_GETSUBSCRIPTIONREQUEST']._serialized_start=1197
  _globals['_GETSUBSCRIPTIONREQUEST']._serialized_end=1246
  _globals['_GETSUBSCRIPTIONRESPONSE']._serialized_start=1248
  _globals['_GETSUBSCRIPTIONRESPONSE']._serialized_end=1365
  _globals['_CANCELSUBSCRIPTIONREQUEST']._serialized_start=1367
  _globals['_CANCELSUBSCRIPTIONREQUEST']._serialized_end=1419
  _globals['_GETCREDITBALANCEREQUEST']._serialized_start=1421
  _globals['_GETCREDITBALANCEREQUEST']._serialized_end=1471
  _globals['_GETCREDITBALANCERESPONSE']._serialized_start=1473
  _globals['_GETCREDITBALANCERESPONSE']._serialized_end=1563
  _globals['_TOKENUSAGELOGENTRY']._serialized_start=1566
  _globals['_TOKENUSAGELOGENTRY']._serialized_end=1776
  _globals['_GETTOKENUSAGEREQUEST']._serialized_start=1779
  _globals['_GETTOKENUSAGEREQUEST']._serialized_end=1955
  _globals['_GETTOKENUSAGERESPONSE']._serialized_start=1957
  _globals['_GETTOKENUSAGERESPONSE']._serialized_end=2075
  _globals['_PLAN']._serialized_start=2078
  _globals['_PLAN']._serialized_end=2215
  _globals['_CREATEPLANREQUEST']._serialized_start=2218
  _globals['_CREATEPLANREQUEST']._serialized_end=2356
  _globals['_LISTPLANSREQUEST']._serialized_start=2358
  _globals['_LISTPLANSREQUEST']._serialized_end=2376
  _globals['_LISTPLANSRESPONSE']._serialized_start=2378
  _globals['_LISTPLANSRESPONSE']._serialized_end=2427
  _globals['_PAYMENTSERVICE']._serialized_start=2813
  _globals['_PAYMENTSERVICE']._serialized_end=3765
# @@protoc_insertion_point(module_scope)
