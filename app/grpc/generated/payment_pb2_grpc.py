# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import payment_pb2 as payment__pb2

GRPC_GENERATED_VERSION = '1.73.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in payment_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class PaymentServiceStub(object):
    """--- Service Definition ---

    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ActivateDefaultPlan = channel.unary_unary(
                '/payment.PaymentService/ActivateDefaultPlan',
                request_serializer=payment__pb2.ActivateDefaultPlanRequest.SerializeToString,
                response_deserializer=payment__pb2.GenericResponse.FromString,
                _registered_method=True)
        self.DeductCredits = channel.unary_unary(
                '/payment.PaymentService/DeductCredits',
                request_serializer=payment__pb2.DeductCreditsRequest.SerializeToString,
                response_deserializer=payment__pb2.DeductCreditsResponse.FromString,
                _registered_method=True)
        self.CreateCheckoutSession = channel.unary_unary(
                '/payment.PaymentService/CreateCheckoutSession',
                request_serializer=payment__pb2.CreateCheckoutSessionRequest.SerializeToString,
                response_deserializer=payment__pb2.CreateCheckoutSessionResponse.FromString,
                _registered_method=True)
        self.CreateCustomerPortalSession = channel.unary_unary(
                '/payment.PaymentService/CreateCustomerPortalSession',
                request_serializer=payment__pb2.CreateCustomerPortalSessionRequest.SerializeToString,
                response_deserializer=payment__pb2.CreateCustomerPortalSessionResponse.FromString,
                _registered_method=True)
        self.HandleStripeWebhook = channel.unary_unary(
                '/payment.PaymentService/HandleStripeWebhook',
                request_serializer=payment__pb2.StripeWebhookRequest.SerializeToString,
                response_deserializer=payment__pb2.GenericResponse.FromString,
                _registered_method=True)
        self.GetSubscription = channel.unary_unary(
                '/payment.PaymentService/GetSubscription',
                request_serializer=payment__pb2.GetSubscriptionRequest.SerializeToString,
                response_deserializer=payment__pb2.GetSubscriptionResponse.FromString,
                _registered_method=True)
        self.CancelSubscription = channel.unary_unary(
                '/payment.PaymentService/CancelSubscription',
                request_serializer=payment__pb2.CancelSubscriptionRequest.SerializeToString,
                response_deserializer=payment__pb2.GenericResponse.FromString,
                _registered_method=True)
        self.GetCreditBalance = channel.unary_unary(
                '/payment.PaymentService/GetCreditBalance',
                request_serializer=payment__pb2.GetCreditBalanceRequest.SerializeToString,
                response_deserializer=payment__pb2.GetCreditBalanceResponse.FromString,
                _registered_method=True)
        self.GetTokenUsage = channel.unary_unary(
                '/payment.PaymentService/GetTokenUsage',
                request_serializer=payment__pb2.GetTokenUsageRequest.SerializeToString,
                response_deserializer=payment__pb2.GetTokenUsageResponse.FromString,
                _registered_method=True)
        self.CreatePlan = channel.unary_unary(
                '/payment.PaymentService/CreatePlan',
                request_serializer=payment__pb2.CreatePlanRequest.SerializeToString,
                response_deserializer=payment__pb2.Plan.FromString,
                _registered_method=True)
        self.ListPlans = channel.unary_unary(
                '/payment.PaymentService/ListPlans',
                request_serializer=payment__pb2.ListPlansRequest.SerializeToString,
                response_deserializer=payment__pb2.ListPlansResponse.FromString,
                _registered_method=True)


class PaymentServiceServicer(object):
    """--- Service Definition ---

    """

    def ActivateDefaultPlan(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeductCredits(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateCheckoutSession(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateCustomerPortalSession(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HandleStripeWebhook(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSubscription(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelSubscription(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCreditBalance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTokenUsage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePlan(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPlans(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PaymentServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ActivateDefaultPlan': grpc.unary_unary_rpc_method_handler(
                    servicer.ActivateDefaultPlan,
                    request_deserializer=payment__pb2.ActivateDefaultPlanRequest.FromString,
                    response_serializer=payment__pb2.GenericResponse.SerializeToString,
            ),
            'DeductCredits': grpc.unary_unary_rpc_method_handler(
                    servicer.DeductCredits,
                    request_deserializer=payment__pb2.DeductCreditsRequest.FromString,
                    response_serializer=payment__pb2.DeductCreditsResponse.SerializeToString,
            ),
            'CreateCheckoutSession': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCheckoutSession,
                    request_deserializer=payment__pb2.CreateCheckoutSessionRequest.FromString,
                    response_serializer=payment__pb2.CreateCheckoutSessionResponse.SerializeToString,
            ),
            'CreateCustomerPortalSession': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCustomerPortalSession,
                    request_deserializer=payment__pb2.CreateCustomerPortalSessionRequest.FromString,
                    response_serializer=payment__pb2.CreateCustomerPortalSessionResponse.SerializeToString,
            ),
            'HandleStripeWebhook': grpc.unary_unary_rpc_method_handler(
                    servicer.HandleStripeWebhook,
                    request_deserializer=payment__pb2.StripeWebhookRequest.FromString,
                    response_serializer=payment__pb2.GenericResponse.SerializeToString,
            ),
            'GetSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSubscription,
                    request_deserializer=payment__pb2.GetSubscriptionRequest.FromString,
                    response_serializer=payment__pb2.GetSubscriptionResponse.SerializeToString,
            ),
            'CancelSubscription': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelSubscription,
                    request_deserializer=payment__pb2.CancelSubscriptionRequest.FromString,
                    response_serializer=payment__pb2.GenericResponse.SerializeToString,
            ),
            'GetCreditBalance': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCreditBalance,
                    request_deserializer=payment__pb2.GetCreditBalanceRequest.FromString,
                    response_serializer=payment__pb2.GetCreditBalanceResponse.SerializeToString,
            ),
            'GetTokenUsage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTokenUsage,
                    request_deserializer=payment__pb2.GetTokenUsageRequest.FromString,
                    response_serializer=payment__pb2.GetTokenUsageResponse.SerializeToString,
            ),
            'CreatePlan': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePlan,
                    request_deserializer=payment__pb2.CreatePlanRequest.FromString,
                    response_serializer=payment__pb2.Plan.SerializeToString,
            ),
            'ListPlans': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPlans,
                    request_deserializer=payment__pb2.ListPlansRequest.FromString,
                    response_serializer=payment__pb2.ListPlansResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'payment.PaymentService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('payment.PaymentService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PaymentService(object):
    """--- Service Definition ---

    """

    @staticmethod
    def ActivateDefaultPlan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/ActivateDefaultPlan',
            payment__pb2.ActivateDefaultPlanRequest.SerializeToString,
            payment__pb2.GenericResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeductCredits(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/DeductCredits',
            payment__pb2.DeductCreditsRequest.SerializeToString,
            payment__pb2.DeductCreditsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateCheckoutSession(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/CreateCheckoutSession',
            payment__pb2.CreateCheckoutSessionRequest.SerializeToString,
            payment__pb2.CreateCheckoutSessionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateCustomerPortalSession(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/CreateCustomerPortalSession',
            payment__pb2.CreateCustomerPortalSessionRequest.SerializeToString,
            payment__pb2.CreateCustomerPortalSessionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HandleStripeWebhook(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/HandleStripeWebhook',
            payment__pb2.StripeWebhookRequest.SerializeToString,
            payment__pb2.GenericResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/GetSubscription',
            payment__pb2.GetSubscriptionRequest.SerializeToString,
            payment__pb2.GetSubscriptionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelSubscription(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/CancelSubscription',
            payment__pb2.CancelSubscriptionRequest.SerializeToString,
            payment__pb2.GenericResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCreditBalance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/GetCreditBalance',
            payment__pb2.GetCreditBalanceRequest.SerializeToString,
            payment__pb2.GetCreditBalanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTokenUsage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/GetTokenUsage',
            payment__pb2.GetTokenUsageRequest.SerializeToString,
            payment__pb2.GetTokenUsageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePlan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/CreatePlan',
            payment__pb2.CreatePlanRequest.SerializeToString,
            payment__pb2.Plan.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListPlans(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/payment.PaymentService/ListPlans',
            payment__pb2.ListPlansRequest.SerializeToString,
            payment__pb2.ListPlansResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
