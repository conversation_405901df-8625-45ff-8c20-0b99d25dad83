"""
Checkout and payment endpoints for the Payment Service.
"""

from fastapi import APIRouter, HTTPException, Depends
import structlog
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.payment import (
    CheckoutSessionRequest,
    CheckoutSessionResponse,
    CustomerPortalRequest,
    CustomerPortalResponse
)
from app.services.stripe_service import stripe_service

logger = structlog.get_logger()
router = APIRouter()


@router.post("/checkout-sessions", response_model=CheckoutSessionResponse)
async def create_checkout_session(
    request: CheckoutSessionRequest,
    db: Session = Depends(get_db)
):
    """
    Create a Stripe Checkout session for subscription or one-time payment.

    Args:
        request: Checkout session request data
        db: Database session

    Returns:
        CheckoutSessionResponse: Session URL and ID
    """
    result = await stripe_service.create_checkout_session(request, db)

    if not result["success"]:
        raise HTTPException(
            status_code=400,
            detail=result.get("error_message", "Failed to create checkout session")
        )

    return CheckoutSessionResponse(
        session_url=result["session_url"],
        session_id=result["session_id"],
        success=result["success"]
    )


@router.post("/customer-portal-sessions", response_model=CustomerPortalResponse)
async def create_customer_portal_session(
    request: CustomerPortalRequest,
    db: Session = Depends(get_db)
):
    """
    Create a Stripe Customer Portal session for subscription management.

    Args:
        request: Customer portal request data
        db: Database session

    Returns:
        CustomerPortalResponse: Portal URL
    """
    result = await stripe_service.create_customer_portal_session(request, db)

    if not result["success"]:
        status_code = 404 if "not found" in result.get("error_message", "").lower() else 400
        raise HTTPException(
            status_code=status_code,
            detail=result.get("error_message", "Failed to create customer portal session")
        )

    return CustomerPortalResponse(
        portal_url=result["portal_url"],
        success=result["success"]
    )
