"""
FastAPI webhook endpoints for Stripe events.
"""

from fastapi import API<PERSON>outer, Request, HTTPException, Depends, BackgroundTasks
from sqlalchemy.orm import Session
import structlog

from app.db.session import get_db
from app.services.webhook_processor import webhook_processor

logger = structlog.get_logger()
router = APIRouter()


@router.post("/stripe-webhook")
async def handle_stripe_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Handle incoming Stripe webhook events.

    Args:
        request: FastAPI request object containing webhook payload
        background_tasks: FastAPI background tasks
        db: Database session

    Returns:
        dict: Success response
    """
    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')

    if not sig_header:
        logger.error("Missing Stripe signature header")
        raise HTTPException(status_code=400, detail="Missing signature header")

    # Verify webhook signature
    event = webhook_processor.verify_webhook_signature(payload, sig_header)
    if not event:
        raise HTTPException(status_code=400, detail="Invalid signature or payload")

    logger.info(
        "Received webhook event",
        event_type=event['type'],
        event_id=event['id']
    )

    # Process event in background to return quickly to Stripe
    background_tasks.add_task(process_webhook_event, event, db)

    return {"status": "received"}


async def process_webhook_event(event: dict, db: Session):
    """
    Process webhook event in background.

    Args:
        event: Stripe event data
        db: Database session
    """
    try:
        success = await webhook_processor.process_event(event, db)
        if success:
            logger.info("Webhook event processed successfully", event_id=event['id'])
        else:
            logger.error("Failed to process webhook event", event_id=event['id'])
    except Exception as e:
        logger.error(
            "Error in background webhook processing",
            event_id=event['id'],
            error=str(e)
        )


async def handle_checkout_session_completed(session, db: Session):
    """
    Handle checkout.session.completed event.
    
    Args:
        session: Stripe session object
        db: Database session
    """
    user_id = session.get('metadata', {}).get('user_id')
    if not user_id:
        logger.error("No user_id in session metadata", session_id=session['id'])
        return
    
    logger.info(
        "Processing checkout session completed",
        session_id=session['id'],
        user_id=user_id,
        mode=session['mode']
    )
    
    if session['mode'] == 'subscription':
        await handle_new_subscription(session, user_id, db)
    elif session['mode'] == 'payment':
        await handle_one_time_payment(session, user_id, db)


async def handle_new_subscription(session, user_id: str, db: Session):
    """
    Handle new subscription creation.
    
    Args:
        session: Stripe session object
        user_id: User ID
        db: Database session
    """
    customer_id = session['customer']
    subscription_id = session['subscription']
    
    # Store customer mapping
    existing_customer = db.query(StripeCustomer).filter(
        StripeCustomer.user_id == user_id
    ).first()
    
    if existing_customer:
        existing_customer.stripe_customer_id = customer_id
        existing_customer.stripe_subscription_id = subscription_id
    else:
        new_customer = StripeCustomer(
            user_id=user_id,
            stripe_customer_id=customer_id,
            stripe_subscription_id=subscription_id
        )
        db.add(new_customer)
    
    db.commit()
    
    # Get subscription details to determine plan
    subscription = stripe.Subscription.retrieve(subscription_id)
    price_id = subscription['items']['data'][0]['price']['id']
    
    # Determine entitlements based on price_id
    entitlements = entitlement_service.get_plan_entitlements(price_id)
    
    # Update user entitlements
    success = await user_service_client.update_user_entitlements(user_id, entitlements)
    
    if success:
        logger.info(
            "Successfully provisioned subscription",
            user_id=user_id,
            subscription_id=subscription_id,
            entitlements=entitlements
        )
    else:
        logger.error(
            "Failed to provision subscription entitlements",
            user_id=user_id,
            subscription_id=subscription_id
        )


async def handle_one_time_payment(session, user_id: str, db: Session):
    """
    Handle one-time payment (credit top-up).
    
    Args:
        session: Stripe session object
        user_id: User ID
        db: Database session
    """
    # Get line items to determine credits purchased
    line_items = stripe.checkout.Session.list_line_items(session['id'])
    
    for item in line_items['data']:
        price_id = item['price']['id']
        quantity = item['quantity']
        
        if price_id == settings.TOPUP_CREDIT_PRICE_ID:
            # Calculate credits (assuming 1000 credits per unit)
            credits_to_add = quantity * 1000
            
            success = await user_service_client.add_user_credits(user_id, credits_to_add)
            
            if success:
                logger.info(
                    "Successfully added credits",
                    user_id=user_id,
                    credits=credits_to_add
                )
            else:
                logger.error(
                    "Failed to add credits",
                    user_id=user_id,
                    credits=credits_to_add
                )


async def handle_invoice_paid(invoice, db: Session):
    """
    Handle invoice.paid event (subscription renewal).
    
    Args:
        invoice: Stripe invoice object
        db: Database session
    """
    customer_id = invoice['customer']
    subscription_id = invoice['subscription']
    
    # Find user by customer_id
    customer_record = db.query(StripeCustomer).filter(
        StripeCustomer.stripe_customer_id == customer_id
    ).first()
    
    if not customer_record:
        logger.error("Customer not found for invoice", customer_id=customer_id)
        return
    
    user_id = customer_record.user_id
    
    # Get subscription to determine plan
    subscription = stripe.Subscription.retrieve(subscription_id)
    price_id = subscription['items']['data'][0]['price']['id']
    
    # Reset monthly credits based on plan
    credits = entitlement_service.get_monthly_credits(price_id)
    
    if credits:
        entitlements = {"credits": credits}
        success = await user_service_client.update_user_entitlements(user_id, entitlements)
        
        if success:
            logger.info(
                "Successfully reset monthly credits",
                user_id=user_id,
                credits=credits
            )
        else:
            logger.error(
                "Failed to reset monthly credits",
                user_id=user_id,
                credits=credits
            )


async def handle_subscription_deleted(subscription, db: Session):
    """
    Handle customer.subscription.deleted event.
    
    Args:
        subscription: Stripe subscription object
        db: Database session
    """
    customer_id = subscription['customer']
    
    # Find user by customer_id
    customer_record = db.query(StripeCustomer).filter(
        StripeCustomer.stripe_customer_id == customer_id
    ).first()
    
    if not customer_record:
        logger.error("Customer not found for subscription deletion", customer_id=customer_id)
        return
    
    user_id = customer_record.user_id
    
    # Update customer record
    customer_record.stripe_subscription_id = None
    db.commit()
    
    # Downgrade to free plan
    free_entitlements = entitlement_service.get_free_plan_entitlements()
    
    success = await user_service_client.update_user_entitlements(user_id, free_entitlements)
    
    if success:
        logger.info(
            "Successfully downgraded to free plan",
            user_id=user_id,
            entitlements=free_entitlements
        )
    else:
        logger.error(
            "Failed to downgrade to free plan",
            user_id=user_id
        )
