"""
Pydantic schemas for payment operations.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
from uuid import UUID


class PaymentMode(str, Enum):
    """Payment mode enumeration."""
    SUBSCRIPTION = "subscription"
    PAYMENT = "payment"


class PaymentStatus(str, Enum):
    """Payment status enumeration."""
    NO_PAYMENT_METHOD = "no_payment_method"
    ACTIVE_SUBSCRIPTION = "active_subscription"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    INCOMPLETE = "incomplete"


class SubscriptionPlan(str, Enum):
    """Subscription plan enumeration."""
    FREE = "free"
    STANDARD = "standard"
    PRO = "pro"


# Request/Response schemas for checkout sessions
class CheckoutSessionRequest(BaseModel):
    """Request schema for creating checkout sessions."""
    user_id: str = Field(..., description="User ID")
    mode: PaymentMode = Field(..., description="Payment mode")
    price_id: str = Field(..., description="Stripe price ID")
    success_url: str = Field(..., description="Success redirect URL")
    cancel_url: str = Field(..., description="Cancel redirect URL")
    quantity: int = Field(default=1, description="Quantity of items")


class CheckoutSessionResponse(BaseModel):
    """Response schema for checkout sessions."""
    session_url: Optional[str] = Field(None, description="Checkout session URL")
    session_id: Optional[str] = Field(None, description="Checkout session ID")
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")


# Request/Response schemas for customer portal
class CustomerPortalRequest(BaseModel):
    """Request schema for customer portal sessions."""
    user_id: str = Field(..., description="User ID")
    return_url: str = Field(..., description="Return URL after portal session")


class CustomerPortalResponse(BaseModel):
    """Response schema for customer portal sessions."""
    portal_url: Optional[str] = Field(None, description="Customer portal URL")
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")


# Request/Response schemas for payment status
class PaymentStatusRequest(BaseModel):
    """Request schema for getting payment status."""
    user_id: str = Field(..., description="User ID")


class SubscriptionInfo(BaseModel):
    """Subscription information schema."""
    subscription_id: Optional[str] = Field(None, description="Stripe subscription ID")
    customer_id: Optional[str] = Field(None, description="Stripe customer ID")
    plan: SubscriptionPlan = Field(..., description="Subscription plan")
    status: PaymentStatus = Field(..., description="Payment status")
    current_period_start: Optional[datetime] = Field(None, description="Current period start")
    current_period_end: Optional[datetime] = Field(None, description="Current period end")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    cancel_at_period_end: bool = Field(default=False, description="Cancel at period end")
    monthly_credits: int = Field(default=0, description="Monthly credit allocation")


class PaymentStatusResponse(BaseModel):
    """Response schema for payment status."""
    status: PaymentStatus = Field(..., description="Payment status")
    subscription: Optional[SubscriptionInfo] = Field(None, description="Subscription information")
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")


# Request/Response schemas for subscription details
class SubscriptionDetailsRequest(BaseModel):
    """Request schema for getting subscription details."""
    user_id: str = Field(..., description="User ID")


class SubscriptionDetailsResponse(BaseModel):
    """Response schema for subscription details."""
    subscription: Optional[SubscriptionInfo] = Field(None, description="Subscription information")
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")


# Request/Response schemas for subscription cancellation
class CancelSubscriptionRequest(BaseModel):
    """Request schema for canceling subscriptions."""
    user_id: str = Field(..., description="User ID")
    immediate: bool = Field(default=False, description="Cancel immediately or at period end")


class CancelSubscriptionResponse(BaseModel):
    """Response schema for subscription cancellation."""
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    cancellation_date: Optional[datetime] = Field(None, description="Cancellation date")


# Webhook schemas
class WebhookEvent(BaseModel):
    """Webhook event schema."""
    id: str = Field(..., description="Event ID")
    type: str = Field(..., description="Event type")
    data: Dict[str, Any] = Field(..., description="Event data")
    created: int = Field(..., description="Event creation timestamp")


class TokenUsageEvent(BaseModel):
    event_id: UUID
    organisation_id: str
    user_id: str
    agent_id: Optional[str] = None
    input_tokens: int
    output_tokens: int
    consumed_credits: float
    description: Optional[str] = None
    timestamp: datetime

# User entitlement schemas for gRPC communication
class UserEntitlements(BaseModel):
    """User entitlements schema."""
    plan: SubscriptionPlan = Field(..., description="Subscription plan")
    credits: int = Field(..., description="Credit balance")
    model_access: str = Field(..., description="Model access level")
    log_retention: str = Field(..., description="Log retention period")
    support_tier: str = Field(..., description="Support tier")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class UpdateEntitlementsRequest(BaseModel):
    """Request schema for updating user entitlements."""
    user_id: str = Field(..., description="User ID")
    entitlements: UserEntitlements = Field(..., description="Entitlements to update")


class UpdateEntitlementsResponse(BaseModel):
    """Response schema for updating user entitlements."""
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")


class AddCreditsRequest(BaseModel):
    """Request schema for adding user credits."""
    user_id: str = Field(..., description="User ID")
    credits: int = Field(..., description="Credits to add")


class AddCreditsResponse(BaseModel):
    """Response schema for adding user credits."""
    success: bool = Field(..., description="Success status")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    new_balance: int = Field(default=0, description="New credit balance")
