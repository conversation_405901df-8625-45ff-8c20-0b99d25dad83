from pydantic import BaseModel, Field
from typing import List, Optional

# Simple folder info model
class FolderInfo(BaseModel):
    id: str
    name: str

# Disconnect Drive models
class DisconnectDriveRequest(BaseModel):
    organisation_id: str

class DisconnectDriveResponse(BaseModel):
    success: bool
    message: str

# Sync Drive models
class SyncDriveRequest(BaseModel):
    user_id: str
    organisation_id: str
    full_sync: bool = False

class SyncDriveResponse(BaseModel):
    success: bool
    message: str
    files_synced: int
    folders_synced: int
    sync_status: str

# List Files models
class ListFilesRequest(BaseModel):
    user_id: str
    folder_id: Optional[str] = None
    page: int = 1
    page_size: int = 10

class DriveFileModel(BaseModel):
    id: str
    name: str
    mime_type: str
    web_view_link: str
    created_time: str
    modified_time: str
    parent_folder_id: str
    size: int
    shared_with: List[str] = []
    is_folder: bool
    child_count: int = 0

class ListFilesResponse(BaseModel):
    success: bool
    message: str
    files: List[DriveFileModel] = []
    total_count: int
    page: int
    page_size: int

# File Details models
class GetFileDetailsRequest(BaseModel):
    user_id: str
    file_id: str

class GetFileDetailsResponse(BaseModel):
    success: bool
    message: str
    file: Optional[DriveFileModel] = None

# Get Folder By ID models
class GetFolderByIdRequest(BaseModel):
    organisation_id: str
    folder_id: str

class GetFolderByIdResponse(BaseModel):
    success: bool
    message: str
    folder: Optional[DriveFileModel] = None
    children: List[DriveFileModel] = []

# Sync Folder By IDs models
class SyncFolderByIdsRequest(BaseModel):
    organisation_id: str
    folder_ids: List[str]

class SyncFolderByIdsResponse(BaseModel):
    success: bool
    message: str
    files_synced: int
    folders_synced: int
    sync_status: str
    synced_folders: List[FolderInfo] = []

# Check File Access models
class CheckFileAccessRequest(BaseModel):
    user_id: str
    file_id: str

class CheckFileAccessResponse(BaseModel):
    success: bool
    message: str
    has_access: bool

# Search Similar Documents models
class SearchSimilarDocumentsRequest(BaseModel):
    user_id: str
    query_text: str
    top_k: int = 5
    agent_id: Optional[str] = None
    organisation_id: str
    file_ids: Optional[List[str]] = None

class SearchResultItem(BaseModel):
    file_id: str
    file_name: str
    mime_type: str
    web_view_link: str
    created_time: str
    modified_time: str
    score: float
    vector_id: str
    chunk_text: str

class SearchSimilarDocumentsResponse(BaseModel):
    success: bool
    message: str
    results: List[SearchResultItem] = []

# Batch Search Similar Documents models
class BatchSearchSimilarDocumentsRequest(BaseModel):
    user_id: str
    query_texts: List[str]
    top_k: int = 5
    agent_id: Optional[str] = None
    organisation_id: str
    file_ids: Optional[List[str]] = None

class QueryResults(BaseModel):
    query_text: str
    results: List[SearchResultItem] = []

class BatchSearchSimilarDocumentsResponse(BaseModel):
    success: bool
    message: str
    query_results: List[QueryResults] = []

# Sync File By URL models
class SyncFileByUrlRequest(BaseModel):
    drive_url: str
    agent_id: str
    user_id: Optional[str] = None
    organisation_id: str

class SyncFileByUrlResponse(BaseModel):
    success: bool
    message: str
    file_id: str
    file_name: str
    sync_status: str

# List Top Level Folders models
class ListTopLevelFoldersRequest(BaseModel):
    organisation_id: str

class ListTopLevelFoldersResponse(BaseModel):
    success: bool
    message: str
    folders: List[FolderInfo] = []