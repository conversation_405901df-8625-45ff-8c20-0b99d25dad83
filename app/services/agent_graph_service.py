import grpc
from typing import Optional, List
from app.core.config import settings
from app.grpc_ import agent_graph_pb2, agent_graph_pb2_grpc
from fastapi import HTTPException


class AgentGraphServiceClient:
    """
    A gRPC client for interacting with the Agent Graph Service.

    This client provides methods to interact with agent graph operations
    such as listing agents by user, organization, and department.

    Attributes:
        channel (grpc.Channel): The gRPC channel for communication.
        stub (agent_graph_pb2_grpc.AgentGraphServiceStub): The gRPC stub for making requests.
    """

    def __init__(self):
        """
        Initializes the AgentGraphServiceClient with a gRPC insecure channel.
        """
        self.channel = grpc.insecure_channel(
            f"{settings.ORGANISATION_SERVICE_HOST}:{settings.ORGANISATION_SERVICE_PORT}"
        )
        self.stub = agent_graph_pb2_grpc.AgentGraphServiceStub(self.channel)

    def _handle_error(self, e: grpc.RpcError):
        """
        Handle gRPC errors and convert them to appropriate HTTP exceptions.

        Args:
            e: The gRPC error

        Raises:
            HTTPException with appropriate status code and detail
        """
        status_code = e.code()
        details = e.details()

        # Map gRPC status codes to HTTP status codes
        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.RESOURCE_EXHAUSTED:
            raise HTTPException(status_code=429, detail=details)
        else:
            # For any other error, return 500 Internal Server Error
            raise HTTPException(status_code=500, detail="Internal server error")

    def _get_visibility_enum(self, visibility: str) -> int:
        """
        Maps a visibility string to its corresponding Visibility enum value.
        
        Args:
            visibility (str): The visibility string (e.g., "private", "public").
            
        Returns:
            int: The corresponding Visibility enum value. Defaults to PRIVATE if not found.
        """
        visibility_map = {
            "private": agent_graph_pb2.Visibility.PRIVATE,
            "public": agent_graph_pb2.Visibility.PUBLIC,
        }
        return visibility_map.get(visibility.lower(), agent_graph_pb2.Visibility.PRIVATE)

    def _get_status_enum(self, status: str) -> int:
        """
        Maps a status string to its corresponding Status enum value.
        
        Args:
            status (str): The status string (e.g., "active", "inactive", "bench").
            
        Returns:
            int: The corresponding Status enum value. Defaults to ACTIVE if not found.
        """
        status_map = {
            "active": agent_graph_pb2.Status.ACTIVE,
            "inactive": agent_graph_pb2.Status.INACTIVE,
            "bench": agent_graph_pb2.Status.BENCH,
        }
        return status_map.get(status.lower(), agent_graph_pb2.Status.ACTIVE)

    def _get_creator_role_enum(self, role: str) -> int:
        """
        Maps a role string to its corresponding CreatorRole enum value.
        
        Args:
            role (str): The role string (e.g., "member", "creator", "viewer").
            
        Returns:
            int: The corresponding CreatorRole enum value. Defaults to MEMBER if not found.
        """
        role_map = {
            "member": agent_graph_pb2.CreatorRole.MEMBER,
            "creator": agent_graph_pb2.CreatorRole.CREATOR,
            "viewer": agent_graph_pb2.CreatorRole.VIEWER,
            "user": agent_graph_pb2.CreatorRole.CREATOR,  # Map "user" to CREATOR
        }
        return role_map.get(role.lower(), agent_graph_pb2.CreatorRole.MEMBER)

    async def create_agent(
        self,
        agent_id: str,
        name: str,
        description: str,
        department: str,
        owner_id: str,
        user_ids: Optional[List[str]] = None,
        visibility: str = "private",
        status: str = "active",
        creator_role: str = "creator"
    ) -> agent_graph_pb2.CreateAgentResponse:
        """
        Create a new agent in the agent graph.

        Args:
            agent_id (str): The unique identifier of the agent
            name (str): The name of the agent
            description (str): The description of the agent
            department (str): The department the agent belongs to
            owner_id (str): The ID of the owner/creator
            user_ids (List[str], optional): List of user IDs that have access to the agent
            visibility (str): The visibility of the agent ("private" or "public")
            status (str): The status of the agent ("active", "inactive", or "bench")
            creator_role (str): The role of the creator ("member", "creator", or "viewer")

        Returns:
            agent_graph_pb2.CreateAgentResponse: Response containing success status and message

        Raises:
            HTTPException: If the gRPC call fails
        """
        try:
            # Create the owner object
            owner = agent_graph_pb2.Owner(id=owner_id)
            
            # Create the request
            request = agent_graph_pb2.CreateAgentRequest(
                id=agent_id,
                name=name,
                description=description,
                department=department,
                owner=owner,
                user_ids=user_ids or [],
                visibility=self._get_visibility_enum(visibility),
                status=self._get_status_enum(status),
                creator_role=self._get_creator_role_enum(creator_role)
            )
            
            response = self.stub.createAgent(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in create_agent: {e}")
            # Don't raise the error, just log it and return a failure response
            # This ensures that agent graph creation failure doesn't break main agent creation
            return agent_graph_pb2.CreateAgentResponse(
                success=False,
                message=f"Failed to create agent in graph: {e.details()}"
            )
        except Exception as e:
            print(f"[ERROR] Unexpected error in create_agent: {e}")
            return agent_graph_pb2.CreateAgentResponse(
                success=False,
                message=f"Failed to create agent in graph: {str(e)}"
            )

    async def list_agents_by_user_id(
        self, user_id: str, page: int = 1, page_size: int = 10
    ) -> agent_graph_pb2.ListAgentsResponse:
        """
        Get all agents that a user has access to.

        Args:
            user_id (str): The ID of the user
            page (int): Page number for pagination (default: 1)
            page_size (int): Number of items per page (default: 10)

        Returns:
            agent_graph_pb2.ListAgentsResponse: Response containing list of agents

        Raises:
            HTTPException: If the gRPC call fails
        """
        try:
            request = agent_graph_pb2.ListAgentsByUserIdRequest(
                owner_id=user_id, page=page, page_size=page_size
            )
            response = self.stub.listAgentsByUserId(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in list_agents_by_user_id: {e}")
            self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in list_agents_by_user_id: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

    async def get_all_agents_from_organisation(
        self, organisation_id: str
    ) -> agent_graph_pb2.ListAgentsResponse:
        """
        Get all agents from all departments of an organization.

        Args:
            organisation_id (str): The ID of the organization

        Returns:
            agent_graph_pb2.ListAgentsResponse: Response containing list of agents

        Raises:
            HTTPException: If the gRPC call fails
        """
        try:
            request = agent_graph_pb2.GetAllAgentsFromOrganisationRequest(
                organisation_id=organisation_id
            )
            response = self.stub.getAllAgentsFromOrganisation(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in get_all_agents_from_organisation: {e}")
            self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in get_all_agents_from_organisation: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

    async def get_agents_from_department(
        self, department_id: str
    ) -> agent_graph_pb2.ListAgentsResponse:
        """
        Get all agents from a specific department.

        Args:
            department_id (str): The ID of the department

        Returns:
            agent_graph_pb2.ListAgentsResponse: Response containing list of agents

        Raises:
            HTTPException: If the gRPC call fails
        """
        try:
            request = agent_graph_pb2.GetAgentsFromDepartmentRequest(
                department_id=department_id
            )
            response = self.stub.getAgentsFromDepartment(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in get_agents_from_department: {e}")
            self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in get_agents_from_department: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

    async def check_user_agent_access(
        self, user_id: str, agent_id: str
    ) -> agent_graph_pb2.CheckAccessResponse:
        """
        Check if a user has access to a specific agent.

        Args:
            user_id (str): The ID of the user
            agent_id (str): The ID of the agent

        Returns:
            agent_graph_pb2.CheckAccessResponse: Response indicating access status

        Raises:
            HTTPException: If the gRPC call fails
        """
        try:
            request = agent_graph_pb2.CheckUserAgentAccessRequest(
                user_id=user_id, agent_id=agent_id
            )
            response = self.stub.checkUserAgentAccess(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in check_user_agent_access: {e}")
            self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in check_user_agent_access: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

    def close(self):
        """Close the gRPC channel."""
        if self.channel:
            self.channel.close()