import grpc
import structlog
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func
from app.db.session import get_db
from app.models.provider import Provider, Model
from app.grpc import provider_pb2

logger = structlog.get_logger()


class ModelFunctionsService:
    """Service for managing model CRUD operations."""

    def __init__(self):
        pass

    def _get_db_session(self) -> Session:
        """Get database session."""
        return next(get_db())

    def _model_to_protobuf(self, model: Model) -> provider_pb2.ModelInfo:
        """Convert Model model to protobuf ModelInfo."""
        provider_info = provider_pb2.ProviderInfo(
            id=model.provider.id,
            provider=model.provider.provider,
            description=model.provider.description or "",
            baseUrl=model.provider.base_url,
            isActive=model.provider.is_active,
            isDefault=model.provider.is_default,
            createdAt=model.provider.created_at.isoformat(),
            updatedAt=model.provider.updated_at.isoformat(),
            modelCount=0  # Not needed in this context
        )

        return provider_pb2.ModelInfo(
            id=model.id,
            providerId=model.provider_id,
            model=model.model,
            modelId=model.model_id,
            description=model.description or "",
            inputPricePerToken=model.input_price_per_token or 0.0,
            outputPricePerToken=model.output_price_per_token or 0.0,
            maxTokens=model.max_tokens or 0,
            contextWindow=model.context_window or 1024,
            temperature=model.temperature or 0.7,
            providerType=model.provider_type,
            isActive=model.is_active,
            isDefault=model.is_default,
            createdAt=model.created_at.isoformat(),
            updatedAt=model.updated_at.isoformat(),
            provider=provider_info
        )

    def _ensure_single_default_per_provider(self, db: Session, provider_id: str, exclude_id: Optional[str] = None):
        """Ensure only one model per provider is marked as default."""
        query = db.query(Model).filter(
            and_(Model.provider_id == provider_id, Model.is_default == True)
        )
        if exclude_id:
            query = query.filter(Model.id != exclude_id)
        
        for model in query.all():
            model.is_default = False
        db.commit()

    def createModel(self, request: provider_pb2.CreateModelRequest, context: grpc.ServicerContext) -> provider_pb2.ModelResponse:
        """Create a new model."""
        try:
            db = self._get_db_session()
            
            # Check if provider exists
            provider = db.query(Provider).filter(Provider.id == request.providerId).first()
            if not provider:
                return provider_pb2.ModelResponse(
                    success=False,
                    message=f"Provider with ID '{request.providerId}' not found"
                )

            # Check if model name already exists for this provider
            existing_model = db.query(Model).filter(
                and_(Model.provider_id == request.providerId, Model.model == request.model)
            ).first()
            if existing_model:
                return provider_pb2.ModelResponse(
                    success=False,
                    message=f"Model with name '{request.model}' already exists for this provider"
                )

            # If this model should be default for the provider, ensure no other model is default
            if request.HasField('isDefault') and request.isDefault:
                self._ensure_single_default_per_provider(db, request.providerId)

            # Create new model
            new_model = Model(
                provider_id=request.providerId,
                model=request.model,
                model_id=request.modelId,
                description=request.description if request.HasField('description') else None,
                input_price_per_token=request.inputPricePerToken if request.HasField('inputPricePerToken') else None,
                output_price_per_token=request.outputPricePerToken if request.HasField('outputPricePerToken') else None,
                max_tokens=request.maxTokens if request.HasField('maxTokens') else None,
                context_window=request.contextWindow if request.HasField('contextWindow') else 1024,
                temperature=request.temperature if request.HasField('temperature') else 0.7,
                provider_type=request.providerType if request.HasField('providerType') else "chat",
                is_active=request.isActive if request.HasField('isActive') else True,
                is_default=request.isDefault if request.HasField('isDefault') else False
            )

            db.add(new_model)
            db.commit()
            db.refresh(new_model)

            # Load the provider relationship
            model_with_provider = db.query(Model).options(joinedload(Model.provider)).filter(Model.id == new_model.id).first()

            logger.info(f"Model created successfully: {new_model.id}")
            return provider_pb2.ModelResponse(
                success=True,
                message="Model created successfully",
                model=self._model_to_protobuf(model_with_provider)
            )

        except Exception as e:
            logger.error(f"Error creating model: {str(e)}")
            db.rollback()
            return provider_pb2.ModelResponse(
                success=False,
                message=f"Failed to create model: {str(e)}"
            )
        finally:
            db.close()

    def getModel(self, request: provider_pb2.GetByIdRequest, context: grpc.ServicerContext) -> provider_pb2.ModelResponse:
        """Get model by ID."""
        try:
            db = self._get_db_session()
            
            model = db.query(Model).options(joinedload(Model.provider)).filter(Model.id == request.id).first()
            if not model:
                return provider_pb2.ModelResponse(
                    success=False,
                    message=f"Model with ID '{request.id}' not found"
                )

            return provider_pb2.ModelResponse(
                success=True,
                message="Model retrieved successfully",
                model=self._model_to_protobuf(model)
            )

        except Exception as e:
            logger.error(f"Error getting model: {str(e)}")
            return provider_pb2.ModelResponse(
                success=False,
                message=f"Failed to get model: {str(e)}"
            )
        finally:
            db.close()

    def updateModel(self, request: provider_pb2.UpdateModelRequest, context: grpc.ServicerContext) -> provider_pb2.ModelResponse:
        """Update an existing model."""
        try:
            db = self._get_db_session()
            
            model = db.query(Model).options(joinedload(Model.provider)).filter(Model.id == request.id).first()
            if not model:
                return provider_pb2.ModelResponse(
                    success=False,
                    message=f"Model with ID '{request.id}' not found"
                )

            # Check if provider exists (if provider_id is being updated)
            if request.HasField('providerId'):
                provider = db.query(Provider).filter(Provider.id == request.providerId).first()
                if not provider:
                    return provider_pb2.ModelResponse(
                        success=False,
                        message=f"Provider with ID '{request.providerId}' not found"
                    )

            # Check if model name already exists for the provider (excluding current model)
            provider_id_to_check = request.providerId if request.HasField('providerId') else model.provider_id
            if request.HasField('model'):
                existing_model = db.query(Model).filter(
                    and_(
                        Model.provider_id == provider_id_to_check,
                        Model.model == request.model,
                        Model.id != request.id
                    )
                ).first()
                if existing_model:
                    return provider_pb2.ModelResponse(
                        success=False,
                        message=f"Model with name '{request.model}' already exists for this provider"
                    )

            # If this model should be default for the provider, ensure no other model is default
            if request.HasField('isDefault') and request.isDefault:
                self._ensure_single_default_per_provider(db, provider_id_to_check, exclude_id=request.id)

            # Update fields
            if request.HasField('providerId'):
                model.provider_id = request.providerId
            if request.HasField('model'):
                model.model = request.model
            if request.HasField('modelId'):
                model.model_id = request.modelId
            if request.HasField('description'):
                model.description = request.description
            if request.HasField('inputPricePerToken'):
                model.input_price_per_token = request.inputPricePerToken
            if request.HasField('outputPricePerToken'):
                model.output_price_per_token = request.outputPricePerToken
            if request.HasField('contextWindow'):
                model.context_window = request.contextWindow
            if request.HasField('maxTokens'):
                model.max_tokens = request.maxTokens
            if request.HasField('temperature'):
                model.temperature = request.temperature
            if request.HasField('providerType'):
                model.provider_type = request.providerType
            if request.HasField('isActive'):
                model.is_active = request.isActive
            if request.HasField('isDefault'):
                model.is_default = request.isDefault

            model.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(model)

            # Reload with provider relationship
            model_with_provider = db.query(Model).options(joinedload(Model.provider)).filter(Model.id == model.id).first()

            logger.info(f"Model updated successfully: {model.id}")
            return provider_pb2.ModelResponse(
                success=True,
                message="Model updated successfully",
                model=self._model_to_protobuf(model_with_provider)
            )

        except Exception as e:
            logger.error(f"Error updating model: {str(e)}")
            db.rollback()
            return provider_pb2.ModelResponse(
                success=False,
                message=f"Failed to update model: {str(e)}"
            )
        finally:
            db.close()

    def deleteModel(self, request: provider_pb2.DeleteRequest, context: grpc.ServicerContext) -> provider_pb2.DeleteResponse:
        """Delete a model."""
        try:
            db = self._get_db_session()
            
            model = db.query(Model).filter(Model.id == request.id).first()
            if not model:
                return provider_pb2.DeleteResponse(
                    success=False,
                    message=f"Model with ID '{request.id}' not found"
                )

            db.delete(model)
            db.commit()

            logger.info(f"Model deleted successfully: {request.id}")
            return provider_pb2.DeleteResponse(
                success=True,
                message="Model deleted successfully"
            )

        except Exception as e:
            logger.error(f"Error deleting model: {str(e)}")
            db.rollback()
            return provider_pb2.DeleteResponse(
                success=False,
                message=f"Failed to delete model: {str(e)}"
            )
        finally:
            db.close()

    def listModels(self, request: provider_pb2.ListRequest, context: grpc.ServicerContext) -> provider_pb2.ListModelsResponse:
        """List models with pagination."""
        try:
            db = self._get_db_session()
            
            # Build query
            query = db.query(Model).options(joinedload(Model.provider))
            
            # Filter by provider_id if specified
            if request.HasField('providerId'):
                # Check if provider exists
                provider = db.query(Provider).filter(Provider.id == request.providerId).first()
                if not provider:
                    return provider_pb2.ListModelsResponse(
                        success=False,
                        message=f"Provider with ID '{request.providerId}' not found",
                        models=[],
                        pagination=provider_pb2.PaginationInfo()
                    )
                query = query.filter(Model.provider_id == request.providerId)
            
            # Filter by active status if specified
            if request.HasField('isActive'):
                query = query.filter(Model.is_active == request.isActive)
            
            # Get total count
            total_count = query.count()
            
            # Apply pagination
            page = max(1, request.page) if request.page > 0 else 1
            page_size = min(100, max(1, request.pageSize)) if request.pageSize > 0 else 10
            offset = (page - 1) * page_size
            
            models = query.offset(offset).limit(page_size).all()
            
            # Convert to protobuf
            model_infos = [self._model_to_protobuf(model) for model in models]
            
            # Calculate pagination info
            total_pages = (total_count + page_size - 1) // page_size
            
            pagination = provider_pb2.PaginationInfo(
                currentPage=page,
                totalPages=total_pages,
                totalItems=total_count,
                pageSize=page_size
            )

            # Create appropriate success message
            if request.HasField('providerId'):
                provider_name = db.query(Provider).filter(Provider.id == request.providerId).first().provider
                message = f"Models for provider '{provider_name}' retrieved successfully"
            else:
                message = "Models retrieved successfully"

            return provider_pb2.ListModelsResponse(
                success=True,
                message=message,
                models=model_infos,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"Error listing models: {str(e)}")
            return provider_pb2.ListModelsResponse(
                success=False,
                message=f"Failed to list models: {str(e)}",
                models=[],
                pagination=provider_pb2.PaginationInfo()
            )
        finally:
            db.close()

    def listModelsByProvider(self, request: provider_pb2.GetByProviderRequest, context: grpc.ServicerContext) -> provider_pb2.ListModelsResponse:
        """List models by provider with pagination."""
        try:
            print("listModelsByProvider")
            db = self._get_db_session()
            
            # Check if provider exists
            provider = db.query(Provider).filter(Provider.id == request.providerId).first()
            if not provider:
                return provider_pb2.ListModelsResponse(
                    success=False,
                    message=f"Provider with ID '{request.providerId}' not found",
                    models=[],
                    pagination=provider_pb2.PaginationInfo()
                )
            print(f"Provider found: {provider.provider}")
            # Build query
            query = db.query(Model).options(joinedload(Model.provider)).filter(Model.provider_id == request.providerId)
            
            # Filter by active status if specified
            if request.HasField('isActive'):
                query = query.filter(Model.is_active == request.isActive)
            
            # Get total count
            total_count = query.count()
            print(f"Total models found: {total_count}")
            # Apply pagination
            page = max(1, request.page) if request.page > 0 else 1
            page_size = min(100, max(1, request.pageSize)) if request.pageSize > 0 else 10
            offset = (page - 1) * page_size
            
            models = query.offset(offset).limit(page_size).all()
            print(f"Models found: {len(models)}")
            # Convert to protobuf
            model_infos = [self._model_to_protobuf(model) for model in models]
            print(f"Models converted to protobuf: {len(model_infos)} models")
            
            # Calculate pagination info
            total_pages = (total_count + page_size - 1) // page_size
            
            pagination = provider_pb2.PaginationInfo(
                currentPage=page,
                totalPages=total_pages,
                totalItems=total_count,
                pageSize=page_size
            )

            return provider_pb2.ListModelsResponse(
                success=True,
                message=f"Models for provider '{provider.provider}' retrieved successfully",
                models=model_infos,
                pagination=pagination
            )
            print("Models retrieved successfully")

        except Exception as e:
            logger.error(f"Error listing models by provider: {str(e)}")
            return provider_pb2.ListModelsResponse(
                success=False,
                message=f"Failed to list models by provider: {str(e)}",
                models=[],
                pagination=provider_pb2.PaginationInfo()
            )
        finally:
            db.close()