# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-06-20 14:10:23 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   Create a dedicated microservice (`payment-service`) for handling all subscription billing and payment logic for the platform using Stripe.
*   The service will orchestrate user subscriptions, process one-time payments (e.g., credit top-ups), and manage the entire billing lifecycle.
*   It acts as a bridge between our platform and the Stripe payment gateway.
*   It is designed to be decoupled from core user data; the `user-service` is the source of truth for user data and entitlements.

## Key Features

*   Creating secure payment sessions with Stripe for subscriptions and one-time purchases.
*   Listening for and processing webhook events from Stripe (e.g., `payment_successful`, `subscription_cancelled`).
*   Translating Stripe events into specific commands and issuing them to the `user-service` to update user entitlements (e.g., credit balance, plan features).

## Overall Architecture

*   **Request Flow:** Client/Frontend → API Gateway → Payment Service → Stripe API
*   **Webhook Flow:** Stripe Webhooks → Payment Service → User Service
*   **API Gateway:** Handles user authentication and routes requests to the `payment-service`, providing the authenticated `user_id`.
*   **User Service:** The single source of truth for all user data and entitlements. The `payment-service` informs the `user-service` about changes to entitlements.
*   **Stripe:** The external payment processor.