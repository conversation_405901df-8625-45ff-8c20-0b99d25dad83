# Active Context

This file tracks the project's current status, including recent changes, current goals, and open questions.
2025-06-20 14:12:39 - Log of updates made.

*

## Current Focus

*   Review and approve the detailed plan for implementing the credit-based payment system.
*   Prepare for implementation phase.

## Recent Changes

*   Initialized Memory Bank.
*   Reviewed `PRD.md` for `payment-service`.
*   Analyzed structure of `analytics-service` models and `api-gateway` routes/services (`agent_routes.py`, `organisation_routes.py`, `user_service.py` client, `analytics_service.py` client).
*   Formulated a detailed plan for the credit-based payment system.
*   [2025-06-20 14:12:39] - Updated with planning details.
*   [2025-06-20 15:10:00] - Decision: Credit Ledger REST API (`GET /organisations/{org_id}/credit-ledger`) to be implemented in `api-gateway` project, not `payment-service`.
*   [2025-06-20 16:37:00] - Implemented additional payment-related REST API endpoints in `api-gateway` (Pro Plan Checkout, Stripe Webhook, Customer Portal, Subscription Details, Cancel Subscription).

## Open Questions/Issues

*   **Token-to-Credit Conversion Rate**: What is the exact rate (e.g., 1000 tokens = 1 credit)?
*   **Pro Plan Details**:
    *   Is the Pro plan a paid upgrade? If so, what is its price and Stripe Price ID?
    *   How is the Pro plan activated (Stripe webhook, manual admin action, user self-serve checkout)?
*   **Insufficient Credits Handling**: What is the precise behavior (block service, allow negative balance, notifications)?
*   **Granular User Consumption Tracking**: Is `analytics-service` sufficient for querying per-user consumption within an org, or is a summary needed elsewhere?
*   **Communication for Deduction**: Confirm direct gRPC from `agent-service` to `payment-service` is the desired pattern for credit deduction.
*   **Idempotency**: Ensure key operations like credit deduction and plan activation are designed to be idempotent.
*   How will the `payment-service` be notified of token consumption for a user/organization after an agent interaction? (Decision: `agent-service` will call `payment-service.DeductCredits`).
*   What is the exact structure for storing user consumption data within an organization? (Decision: `analytics-service` will store detailed logs; `payment-service` will have a `CreditLedger`).
*   Are there specific requirements for how often consumption data needs to be updated/synced? (Decision: Real-time deduction after each interaction).