# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-20 14:10:57 - Log of updates made.

*

## Decision

*   Initialize Memory Bank for the `payment-service` project.

## Rationale

*   To maintain project context, track progress, log decisions, and document system patterns effectively throughout the development lifecycle.

## Implementation Details

*   Created `memory-bank/productContext.md` with initial content derived from `PRD.md`.
*   Created `memory-bank/activeContext.md` with current focus and open questions.
*   Created `memory-bank/progress.md` outlining completed, current, and next tasks.
*   This `memory-bank/decisionLog.md` file.
*   Created `memory-bank/systemPatterns.md`.

---
[2025-06-20 14:14:30] - Detailed Plan for Credit-Based Payment System

## Decision

*   Adopt a microservice-based approach for the credit payment system, involving `payment-service`, `user-service`, `organisation-service`, `agent-service`, and `api-gateway`.
*   `user-service` will hold the authoritative `credit_balance` for an organisation.
*   `payment-service` will manage plan definitions, the credit ledger, and orchestrate credit deduction logic by calling `user-service`.
*   `organisation-service` will trigger default plan activation in `payment-service` upon new organisation creation.
*   `agent-service` (or equivalent) will calculate token usage, convert to credits, and call `payment-service` to deduct credits. It will also log detailed usage to `analytics-service`.
*   `analytics-service` will be used for detailed logging of usage events, including token counts and credits consumed.

## Rationale

*   Leverages existing microservice architecture.
*   Decouples payment logic (`payment-service`) from core user data (`user-service`).
*   Provides a clear flow for credit allocation and deduction.
*   Utilizes `analytics-service` for detailed usage tracking without bloating `payment-service` or `user-service` with fine-grained logs not directly related to billing state.

## Implementation Details

*   **User Service**: Add `credit_balance`, `current_plan_id` to Organisation model. New gRPC endpoints: `SetInitialOrganisationCredits`, `UpdateOrganisationCreditBalance`, `GetOrganisationCreditBalance`, `AssignPlanToOrganisation`.
*   **Organisation Service**: Modify `CreateOrganisation` to call `payment-service.ActivateDefaultPlan`.
*   **Agent Service**: Implement token calculation, credit conversion. Call `payment-service.DeductCredits` and `analytics-service.RecordUsageEvent`.
*   **Payment Service**: New DB tables: `Plans`, `CreditLedger`. New gRPC endpoints: `ActivateDefaultPlan`, `DeductCredits`. Define "Free" (20 credits) and "Pro" (100 credits) plans.
*   **API Gateway**: Route new `payment-service` endpoints if externally needed.
*   **Analytics Service**: Ensure `UsageEvent` or `RecordApiRequest` can store `org_id`, token details, and `credits_used`.